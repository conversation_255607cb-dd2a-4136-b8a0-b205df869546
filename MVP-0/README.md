# Alpha BNPL - Buy Now, Pay Later for Paraguay

[![CI](https://github.com/alpha-bnpl/alpha-bnpl/actions/workflows/ci.yml/badge.svg)](https://github.com/alpha-bnpl/alpha-bnpl/actions/workflows/ci.yml)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

Alpha BNPL is a modern Buy-Now-Pay-Later platform designed specifically for the Paraguayan market. Built with cutting-edge technology, it provides instant credit decisions, seamless QR-based payments through SIPAP integration, and a user-friendly experience optimized for both desktop and mobile devices.

## 🚀 Features

- **Magic-Link Authentication**: Passwordless login system using Supabase Auth
- **Instant KYC Verification**: Real-time identity verification via Equifax API
- **QR Code Payments**: SIPAP-integrated QR codes for bank app payments
- **Smart Loan Engine**: Automated credit scoring and loan approval
- **Real-time Dashboard**: User-friendly interface for loan management
- **Admin Console**: Comprehensive admin tools for loan oversight
- **Mobile-First PWA**: Progressive Web App optimized for mobile devices
- **Analytics Integration**: PostHog analytics for user behavior tracking

## 🏗️ Architecture

This is a monorepo built with:

- **Frontend**: Next.js 14 with App Router, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Authentication**: Supabase Auth with magic-link
- **Database**: PostgreSQL with Prisma ORM
- **Payments**: SIPAP QR code integration
- **Analytics**: PostHog for event tracking
- **Testing**: Cypress for E2E testing, Jest for unit tests
- **CI/CD**: GitHub Actions with automated deployments

## 📦 Project Structure

```
├── apps/
│   └── web/                 # Next.js web application
│       ├── src/
│       │   ├── app/         # App Router pages and API routes
│       │   ├── components/  # React components
│       │   ├── lib/         # Utility functions and configurations
│       │   └── providers/   # Context providers
│       ├── cypress/         # E2E tests
│       └── prisma/          # Database schema and migrations
├── packages/                # Shared packages (future)
├── docs/                    # Documentation
└── .github/                 # GitHub Actions workflows
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+ and npm
- PostgreSQL (or use Supabase cloud)
- Git

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/alpha-bnpl/alpha-bnpl.git
   cd alpha-bnpl
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp apps/web/.env.example apps/web/.env.local
   ```
   
   Fill in your environment variables:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   
   # Database
   DATABASE_URL=your_database_url
   
   # Analytics
   NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
   NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
   
   # Payment Integration
   ALPHA_CBU=your_cbu_number
   NEXT_PUBLIC_ALPHA_CBU=your_cbu_number
   
   # KYC Integration
   EQUIFAX_API_KEY=your_equifax_api_key
   EQUIFAX_API_URL=https://api.equifax.com.py
   ```

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npm run db:generate
   
   # Run database migrations
   npm run db:push
   
   # Seed database (optional)
   npm run db:seed
   ```

5. **Start Development Server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint errors
- `npm run type-check` - Run TypeScript type checking
- `npm run test` - Run unit tests
- `npm run test:e2e` - Run Cypress E2E tests
- `npm run test:e2e:headless` - Run E2E tests in headless mode
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:studio` - Open Prisma Studio

## 🧪 Testing

### Unit Tests
```bash
npm run test
```

### E2E Tests
```bash
# Interactive mode
npm run test:e2e

# Headless mode
npm run test:e2e:headless
```

### Test Coverage
```bash
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
# Build the application
npm run build

# Start production server
npm run start
```

## 📊 Phase 1 Implementation

The current implementation includes:

- ✅ **Monorepo Setup**: TurboRepo configuration with proper tooling
- ✅ **Database Schema**: Complete PostgreSQL schema with Prisma
- ✅ **Authentication**: Magic-link auth with Supabase
- ✅ **KYC Integration**: Mock KYC endpoint (ready for Equifax integration)
- ✅ **Checkout API**: Amount validation and QR generation
- ✅ **QR Component**: Dynamic SIPAP QR code generation
- ✅ **Design System**: Tailwind-based UI components
- ✅ **Analytics**: PostHog integration with event tracking
- ✅ **E2E Testing**: Comprehensive Cypress test suite
- ✅ **CI/CD Pipeline**: GitHub Actions for testing and deployment

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | Yes |
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `NEXT_PUBLIC_POSTHOG_KEY` | PostHog project key | No |
| `ALPHA_CBU` | Alpha's CBU for payments | Yes |
| `EQUIFAX_API_KEY` | Equifax API key | No (Phase 2) |

### Database Schema

The database includes the following main tables:
- `users` - User profiles and authentication
- `kyc_checks` - KYC verification records
- `loans` - Loan applications and details
- `repayments` - Payment history and schedules
- `events` - Analytics and audit trail

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Follow the existing code style
- Use TypeScript for type safety
- Write tests for new features
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:

- 📧 Email: <EMAIL>
- 📱 WhatsApp: +595 21 123 4567
- 🌐 Website: [alpha.com.py](https://alpha.com.py)

## 🗺️ Roadmap

### Phase 2 (Weeks 6-11)
- Real Equifax KYC integration
- Advanced loan engine with ML scoring
- Merchant dashboard and API
- Payment webhooks and reconciliation

### Phase 3 (Weeks 12-17)
- Multi-installment loans
- Credit limit management
- SMS/WhatsApp notifications
- Advanced analytics dashboard

### Phase 4 (Weeks 18-20)
- Mobile app (React Native)
- Biometric authentication
- Offline payment support
- Advanced fraud detection

### Phase 5 (Weeks 21-23)
- Open banking integration
- Marketplace partnerships
- Advanced reporting
- Compliance automation

---

Made with ❤️ in Paraguay 🇵🇾 