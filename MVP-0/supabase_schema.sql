-- Alpha BNPL Database Schema for Supabase
-- This script creates all necessary tables, indexes, and security policies
-- Run this in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types/enums (with IF NOT EXISTS equivalent)
DO $$ BEGIN
    CREATE TYPE kyc_status AS ENUM ('pending', 'approved', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE merchant_status AS ENUM ('pending', 'active', 'suspended');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE loan_status AS ENUM ('pending', 'approved', 'active', 'completed', 'defaulted', 'rejected', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE repayment_status AS ENUM ('pending', 'completed', 'failed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =====================================================
-- USERS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    full_name VARCHAR(255),
    document_number VARCHAR(50),
    kyc_status kyc_status DEFAULT 'pending',
    credit_limit DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for users table (with IF NOT EXISTS)
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_document_number ON public.users(document_number);
CREATE INDEX IF NOT EXISTS idx_users_kyc_status ON public.users(kyc_status);

-- =====================================================
-- MERCHANTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.merchants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    website VARCHAR(255),
    business_type VARCHAR(100),
    contact_name VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    webhook_url VARCHAR(500),
    api_key VARCHAR(255) UNIQUE NOT NULL,
    webhook_secret VARCHAR(255) NOT NULL,
    status merchant_status DEFAULT 'pending',
    trust_score INTEGER DEFAULT 50 CHECK (trust_score >= 0 AND trust_score <= 100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for merchants table
CREATE INDEX IF NOT EXISTS idx_merchants_email ON public.merchants(email);
CREATE INDEX IF NOT EXISTS idx_merchants_api_key ON public.merchants(api_key);
CREATE INDEX IF NOT EXISTS idx_merchants_status ON public.merchants(status);
CREATE INDEX IF NOT EXISTS idx_merchants_trust_score ON public.merchants(trust_score);

-- =====================================================
-- LOANS TABLE - DROP AND RECREATE TO FIX CONSTRAINT
-- =====================================================
-- First, let's drop the existing table if it has the wrong constraint
DROP TABLE IF EXISTS public.loans CASCADE;

CREATE TABLE public.loans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    merchant_id UUID REFERENCES public.merchants(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    status loan_status DEFAULT 'pending',
    interest_rate DECIMAL(5,2) DEFAULT 0.00 CHECK (interest_rate >= 0),
    installments INTEGER DEFAULT 1 CHECK (installments >= 0),
    installment_amount DECIMAL(10,2),
    due_date TIMESTAMPTZ NOT NULL,
    product_details JSONB,
    merchant_data JSONB,
    decision_data JSONB,
    status_reason TEXT,
    completed_at TIMESTAMPTZ,
    checkout_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for loans table
CREATE INDEX IF NOT EXISTS idx_loans_user_id ON public.loans(user_id);
CREATE INDEX IF NOT EXISTS idx_loans_merchant_id ON public.loans(merchant_id);
CREATE INDEX IF NOT EXISTS idx_loans_status ON public.loans(status);
CREATE INDEX IF NOT EXISTS idx_loans_due_date ON public.loans(due_date);
CREATE INDEX IF NOT EXISTS idx_loans_amount ON public.loans(amount);
CREATE INDEX IF NOT EXISTS idx_loans_created_at ON public.loans(created_at);

-- =====================================================
-- REPAYMENTS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.repayments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    loan_id UUID NOT NULL REFERENCES public.loans(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    status repayment_status DEFAULT 'pending',
    payment_method VARCHAR(50) NOT NULL,
    reference_number VARCHAR(255),
    notes TEXT,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for repayments table
CREATE INDEX IF NOT EXISTS idx_repayments_loan_id ON public.repayments(loan_id);
CREATE INDEX IF NOT EXISTS idx_repayments_user_id ON public.repayments(user_id);
CREATE INDEX IF NOT EXISTS idx_repayments_status ON public.repayments(status);
CREATE INDEX IF NOT EXISTS idx_repayments_created_at ON public.repayments(created_at);
CREATE INDEX IF NOT EXISTS idx_repayments_processed_at ON public.repayments(processed_at);

-- =====================================================
-- KYC_CHECKS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.kyc_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    status kyc_status DEFAULT 'pending',
    provider VARCHAR(100) NOT NULL,
    provider_response JSONB,
    document_number VARCHAR(50),
    full_name VARCHAR(255),
    email VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for kyc_checks table
CREATE INDEX IF NOT EXISTS idx_kyc_checks_user_id ON public.kyc_checks(user_id);
CREATE INDEX IF NOT EXISTS idx_kyc_checks_status ON public.kyc_checks(status);
CREATE INDEX IF NOT EXISTS idx_kyc_checks_provider ON public.kyc_checks(provider);

-- =====================================================
-- EVENTS TABLE (for analytics and audit trail)
-- =====================================================
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(100) NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for events table
CREATE INDEX IF NOT EXISTS idx_events_type ON public.events(type);
CREATE INDEX IF NOT EXISTS idx_events_user_id ON public.events(user_id);
CREATE INDEX IF NOT EXISTS idx_events_created_at ON public.events(created_at);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT TIMESTAMPS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables with updated_at (drop first to avoid conflicts)
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS update_merchants_updated_at ON public.merchants;
DROP TRIGGER IF EXISTS update_loans_updated_at ON public.loans;
DROP TRIGGER IF EXISTS update_repayments_updated_at ON public.repayments;
DROP TRIGGER IF EXISTS update_kyc_checks_updated_at ON public.kyc_checks;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON public.merchants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_loans_updated_at BEFORE UPDATE ON public.loans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_repayments_updated_at BEFORE UPDATE ON public.repayments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kyc_checks_updated_at BEFORE UPDATE ON public.kyc_checks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.loans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repayments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kyc_checks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view own loans" ON public.loans;
DROP POLICY IF EXISTS "Users can update own loans" ON public.loans;
DROP POLICY IF EXISTS "Users can view own repayments" ON public.repayments;
DROP POLICY IF EXISTS "Users can create own repayments" ON public.repayments;
DROP POLICY IF EXISTS "Users can view own kyc_checks" ON public.kyc_checks;
DROP POLICY IF EXISTS "Users can create own kyc_checks" ON public.kyc_checks;
DROP POLICY IF EXISTS "Users can view own events" ON public.events;
DROP POLICY IF EXISTS "Service role can access all users" ON public.users;
DROP POLICY IF EXISTS "Service role can access all merchants" ON public.merchants;
DROP POLICY IF EXISTS "Service role can access all loans" ON public.loans;
DROP POLICY IF EXISTS "Service role can access all repayments" ON public.repayments;
DROP POLICY IF EXISTS "Service role can access all kyc_checks" ON public.kyc_checks;
DROP POLICY IF EXISTS "Service role can access all events" ON public.events;

-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Users can only see their own loans
CREATE POLICY "Users can view own loans" ON public.loans
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own loans" ON public.loans
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can only see their own repayments
CREATE POLICY "Users can view own repayments" ON public.repayments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own repayments" ON public.repayments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only see their own KYC checks
CREATE POLICY "Users can view own kyc_checks" ON public.kyc_checks
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own kyc_checks" ON public.kyc_checks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only see their own events
CREATE POLICY "Users can view own events" ON public.events
    FOR SELECT USING (auth.uid() = user_id);

-- Service role can access all data (for API endpoints)
CREATE POLICY "Service role can access all users" ON public.users
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all merchants" ON public.merchants
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all loans" ON public.loans
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all repayments" ON public.repayments
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all kyc_checks" ON public.kyc_checks
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all events" ON public.events
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =====================================================
-- FUNCTIONS FOR BUSINESS LOGIC
-- =====================================================

-- Function to calculate loan metrics
CREATE OR REPLACE FUNCTION get_loan_metrics(loan_id UUID)
RETURNS TABLE(
    total_paid DECIMAL(10,2),
    remaining_balance DECIMAL(10,2),
    is_overdue BOOLEAN,
    days_overdue INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(r.amount), 0) as total_paid,
        l.amount - COALESCE(SUM(r.amount), 0) as remaining_balance,
        (l.due_date < NOW() AND l.status IN ('approved', 'active')) as is_overdue,
        CASE 
            WHEN l.due_date < NOW() AND l.status IN ('approved', 'active') 
            THEN EXTRACT(DAY FROM NOW() - l.due_date)::INTEGER
            ELSE 0
        END as days_overdue
    FROM public.loans l
    LEFT JOIN public.repayments r ON l.id = r.loan_id AND r.status = 'completed'
    WHERE l.id = loan_id
    GROUP BY l.id, l.amount, l.due_date, l.status;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user credit limit based on loan history
CREATE OR REPLACE FUNCTION update_user_credit_limit(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    completed_loans INTEGER;
    total_repaid DECIMAL(10,2);
    new_credit_limit DECIMAL(10,2);
BEGIN
    -- Count completed loans and total repaid amount
    SELECT 
        COUNT(*) FILTER (WHERE status = 'completed'),
        COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0)
    INTO completed_loans, total_repaid
    FROM public.loans
    WHERE user_id = user_uuid;
    
    -- Calculate new credit limit based on history
    new_credit_limit := LEAST(
        1000 + (completed_loans * 500) + (total_repaid * 0.1),
        10000 -- Maximum credit limit
    );
    
    -- Update user's credit limit
    UPDATE public.users 
    SET credit_limit = new_credit_limit,
        updated_at = NOW()
    WHERE id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Insert sample merchant for testing
INSERT INTO public.merchants (
    name, email, website, business_type, contact_name, 
    api_key, webhook_secret, status, trust_score
) VALUES (
    'Demo Store', '<EMAIL>', 'https://demo.alphabnpl.com',
    'E-commerce', 'Demo Manager',
    'demo_api_key_12345', 'demo_webhook_secret_67890',
    'active', 85
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions for authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant permissions for service role
GRANT ALL ON SCHEMA public TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;

-- =====================================================
-- VIEWS FOR ANALYTICS (Optional)
-- =====================================================

-- Drop existing views to avoid conflicts
DROP VIEW IF EXISTS loan_analytics;
DROP VIEW IF EXISTS merchant_performance;

-- View for loan analytics
CREATE VIEW loan_analytics AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    COUNT(*) as total_loans,
    COUNT(*) FILTER (WHERE status = 'approved') as approved_loans,
    COUNT(*) FILTER (WHERE status = 'rejected') as rejected_loans,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_loans,
    AVG(amount) as avg_loan_amount,
    SUM(amount) as total_loan_volume
FROM public.loans
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

-- View for merchant performance
CREATE VIEW merchant_performance AS
SELECT 
    m.id,
    m.name,
    COUNT(l.id) as total_loans,
    COUNT(l.id) FILTER (WHERE l.status = 'approved') as approved_loans,
    COUNT(l.id) FILTER (WHERE l.status = 'completed') as completed_loans,
    AVG(l.amount) as avg_loan_amount,
    SUM(l.amount) as total_volume,
    m.trust_score
FROM public.merchants m
LEFT JOIN public.loans l ON m.id = l.merchant_id
GROUP BY m.id, m.name, m.trust_score
ORDER BY total_volume DESC;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- Log completion with proper JSONB casting
INSERT INTO public.events (type, metadata) 
VALUES ('schema_created', jsonb_build_object(
    'version', '1.0',
    'tables_created', 6,
    'views_created', 2,
    'functions_created', 2,
    'created_at', NOW()
));

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🎉 Alpha BNPL database schema created successfully!';
    RAISE NOTICE '📋 Tables created: users, merchants, loans, repayments, kyc_checks, events';
    RAISE NOTICE '📊 Views created: loan_analytics, merchant_performance';
    RAISE NOTICE '⚙️  Functions created: get_loan_metrics, update_user_credit_limit';
    RAISE NOTICE '🔒 Indexes, triggers, and RLS policies have been set up.';
    RAISE NOTICE '🏪 Demo merchant added with API key: demo_api_key_12345';
    RAISE NOTICE '✅ Fixed installments constraint to allow 0 for rejected loans';
    RAISE NOTICE '🚀 You can now run the sample_data.sql script to populate test data.';
END $$; 