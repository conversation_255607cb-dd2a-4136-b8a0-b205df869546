-- Alpha BNPL Complete Database Reset and Population Script
-- This script completely resets the database and populates it with sample data
-- Run this in your Supabase SQL editor

-- =====================================================
-- STEP 1: COMPLETE CLEANUP - DROP EVERYTHING
-- =====================================================

-- Drop all views first
DROP VIEW IF EXISTS public.merchant_performance CASCADE;
DROP VIEW IF EXISTS public.loan_analytics CASCADE;

-- Drop all functions
DROP FUNCTION IF EXISTS public.update_user_credit_limit(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_loan_metrics(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;

-- Drop all tables (in correct dependency order)
DROP TABLE IF EXISTS public.events CASCADE;
DROP TABLE IF EXISTS public.repayments CASCADE;
DROP TABLE IF EXISTS public.kyc_checks CASCADE;
DROP TABLE IF EXISTS public.loans CASCADE;
DROP TABLE IF EXISTS public.merchants CASCADE;
DROP TABLE IF EXISTS public.users CASCADE;

-- Drop all custom types
DROP TYPE IF EXISTS public.repayment_status CASCADE;
DROP TYPE IF EXISTS public.loan_status CASCADE;
DROP TYPE IF EXISTS public.merchant_status CASCADE;
DROP TYPE IF EXISTS public.kyc_status CASCADE;

-- =====================================================
-- STEP 2: RECREATE SCHEMA WITH CORRECT CONSTRAINTS
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types/enums
CREATE TYPE public.kyc_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE public.merchant_status AS ENUM ('pending', 'active', 'suspended');
CREATE TYPE public.loan_status AS ENUM ('pending', 'approved', 'active', 'completed', 'defaulted', 'rejected', 'cancelled');
CREATE TYPE public.repayment_status AS ENUM ('pending', 'completed', 'failed');

-- =====================================================
-- USERS TABLE
-- =====================================================
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    full_name TEXT,
    document_number TEXT UNIQUE,
    kyc_status kyc_status DEFAULT 'pending',
    credit_limit DECIMAL(10,2) DEFAULT 0.00 CHECK (credit_limit >= 0),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- MERCHANTS TABLE
-- =====================================================
CREATE TABLE public.merchants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    website TEXT,
    business_type TEXT,
    contact_name TEXT,
    phone TEXT,
    address TEXT,
    webhook_url TEXT,
    api_key TEXT UNIQUE NOT NULL,
    webhook_secret TEXT,
    status merchant_status DEFAULT 'pending',
    trust_score INTEGER DEFAULT 50 CHECK (trust_score >= 0 AND trust_score <= 100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- LOANS TABLE (WITH CORRECT CONSTRAINTS)
-- =====================================================
CREATE TABLE public.loans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    merchant_id UUID REFERENCES public.merchants(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    status loan_status DEFAULT 'pending',
    interest_rate DECIMAL(5,2) DEFAULT 0.00 CHECK (interest_rate >= 0),
    installments INTEGER DEFAULT 1 CHECK (installments >= 0),  -- ALLOWS 0 FOR REJECTED/CANCELLED
    installment_amount DECIMAL(10,2),
    due_date TIMESTAMPTZ NOT NULL,
    product_details JSONB,
    merchant_data JSONB,
    decision_data JSONB,
    status_reason TEXT,
    completed_at TIMESTAMPTZ,
    checkout_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- REPAYMENTS TABLE
-- =====================================================
CREATE TABLE public.repayments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    loan_id UUID NOT NULL REFERENCES public.loans(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    status repayment_status DEFAULT 'pending',
    payment_method TEXT,
    reference_number TEXT,
    notes TEXT,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- KYC CHECKS TABLE
-- =====================================================
CREATE TABLE public.kyc_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    status kyc_status DEFAULT 'pending',
    provider TEXT,
    provider_response JSONB,
    document_number TEXT,
    full_name TEXT,
    email TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- EVENTS TABLE
-- =====================================================
CREATE TABLE public.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type TEXT NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_document_number ON public.users(document_number);
CREATE INDEX idx_users_kyc_status ON public.users(kyc_status);

CREATE INDEX idx_merchants_email ON public.merchants(email);
CREATE INDEX idx_merchants_api_key ON public.merchants(api_key);
CREATE INDEX idx_merchants_status ON public.merchants(status);
CREATE INDEX idx_merchants_trust_score ON public.merchants(trust_score);

CREATE INDEX idx_loans_user_id ON public.loans(user_id);
CREATE INDEX idx_loans_merchant_id ON public.loans(merchant_id);
CREATE INDEX idx_loans_status ON public.loans(status);
CREATE INDEX idx_loans_due_date ON public.loans(due_date);
CREATE INDEX idx_loans_amount ON public.loans(amount);
CREATE INDEX idx_loans_created_at ON public.loans(created_at);

CREATE INDEX idx_repayments_loan_id ON public.repayments(loan_id);
CREATE INDEX idx_repayments_user_id ON public.repayments(user_id);
CREATE INDEX idx_repayments_status ON public.repayments(status);
CREATE INDEX idx_repayments_created_at ON public.repayments(created_at);
CREATE INDEX idx_repayments_processed_at ON public.repayments(processed_at);

CREATE INDEX idx_kyc_checks_user_id ON public.kyc_checks(user_id);
CREATE INDEX idx_kyc_checks_status ON public.kyc_checks(status);
CREATE INDEX idx_kyc_checks_provider ON public.kyc_checks(provider);

CREATE INDEX idx_events_type ON public.events(type);
CREATE INDEX idx_events_user_id ON public.events(user_id);
CREATE INDEX idx_events_created_at ON public.events(created_at);

-- =====================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON public.merchants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_loans_updated_at BEFORE UPDATE ON public.loans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_repayments_updated_at BEFORE UPDATE ON public.repayments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kyc_checks_updated_at BEFORE UPDATE ON public.kyc_checks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- CREATE BUSINESS LOGIC FUNCTIONS
-- =====================================================
CREATE OR REPLACE FUNCTION get_loan_metrics(loan_id UUID)
RETURNS JSON AS $$
DECLARE
    loan_record RECORD;
    repayments_total DECIMAL(10,2);
    repayments_count INTEGER;
    result JSON;
BEGIN
    -- Get loan details
    SELECT * INTO loan_record FROM public.loans WHERE id = loan_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object('error', 'Loan not found');
    END IF;
    
    -- Get repayment statistics
    SELECT 
        COALESCE(SUM(amount), 0),
        COUNT(*)
    INTO repayments_total, repayments_count
    FROM public.repayments 
    WHERE loan_id = loan_record.id AND status = 'completed';
    
    -- Build result
    result := json_build_object(
        'loan_id', loan_record.id,
        'amount', loan_record.amount,
        'status', loan_record.status,
        'installments', loan_record.installments,
        'installment_amount', loan_record.installment_amount,
        'total_paid', repayments_total,
        'payments_made', repayments_count,
        'remaining_balance', loan_record.amount - repayments_total,
        'is_overdue', (loan_record.due_date < NOW() AND loan_record.status = 'active')
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_user_credit_limit(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    completed_loans_count INTEGER;
    total_repaid DECIMAL(10,2);
    current_limit DECIMAL(10,2);
    new_limit DECIMAL(10,2);
BEGIN
    -- Get current credit limit
    SELECT credit_limit INTO current_limit FROM public.users WHERE id = user_uuid;
    
    -- Count completed loans and total repaid
    SELECT 
        COUNT(*),
        COALESCE(SUM(amount), 0)
    INTO completed_loans_count, total_repaid
    FROM public.loans 
    WHERE user_id = user_uuid AND status = 'completed';
    
    -- Calculate new credit limit based on payment history
    new_limit := current_limit;
    
    IF completed_loans_count >= 2 AND total_repaid >= 1000 THEN
        new_limit := current_limit + (completed_loans_count * 500);
    ELSIF completed_loans_count >= 1 AND total_repaid >= 500 THEN
        new_limit := current_limit + 250;
    END IF;
    
    -- Cap at reasonable maximum
    IF new_limit > 10000 THEN
        new_limit := 10000;
    END IF;
    
    -- Update if changed
    IF new_limit != current_limit THEN
        UPDATE public.users SET credit_limit = new_limit WHERE id = user_uuid;
        
        -- Log the change
        INSERT INTO public.events (type, user_id, metadata)
        VALUES ('credit_limit_updated', user_uuid, 
                json_build_object('old_limit', current_limit, 'new_limit', new_limit, 'reason', 'payment_history'));
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE ANALYTICS VIEWS
-- =====================================================
CREATE VIEW loan_analytics AS
SELECT 
    l.status,
    COUNT(*) as loan_count,
    SUM(l.amount) as total_amount,
    AVG(l.amount) as avg_amount,
    SUM(CASE WHEN l.due_date < NOW() AND l.status = 'active' THEN 1 ELSE 0 END) as overdue_count
FROM public.loans l
GROUP BY l.status;

CREATE VIEW merchant_performance AS
SELECT 
    m.id,
    m.name,
    m.status,
    m.trust_score,
    COUNT(l.id) as total_loans,
    SUM(CASE WHEN l.status = 'completed' THEN 1 ELSE 0 END) as completed_loans,
    SUM(CASE WHEN l.status = 'rejected' THEN 1 ELSE 0 END) as rejected_loans,
    SUM(l.amount) as total_loan_amount
FROM public.merchants m
LEFT JOIN public.loans l ON m.id = l.merchant_id
GROUP BY m.id, m.name, m.status, m.trust_score;

-- =====================================================
-- STEP 3: POPULATE WITH SAMPLE DATA
-- =====================================================

-- Insert users with various KYC statuses and credit limits
INSERT INTO public.users (id, email, phone, full_name, document_number, kyc_status, credit_limit, created_at) VALUES
-- Approved users with good credit
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'María Alejandra González Ruiz', '12345678', 'approved', 5000.00, '2024-01-15 10:00:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Carlos Eduardo Rodríguez López', '23456789', 'approved', 3500.00, '2024-01-20 14:30:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Ana Lucía Martínez Torres', '34567890', 'approved', 2800.00, '2024-02-01 09:15:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Diego Alejandro Ramírez Castillo', '45678901', 'approved', 4200.00, '2024-02-10 11:45:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Sofía Isabel Herrera Morales', '56789012', 'approved', 6000.00, '2024-02-15 16:20:00+00'),
-- Pending KYC users
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-305-678-9012', 'Luis Fernando Castro Jiménez', '67890123', 'pending', 0.00, '2024-06-01 08:30:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Andrea Carolina López Vargas', '78901234', 'pending', 0.00, '2024-06-10 13:15:00+00'),
-- Rejected user
('550e8400-e29b-41d4-a716-446655440008', '<EMAIL>', '+57-************', 'Usuario Rechazado Test', '89012345', 'rejected', 0.00, '2024-05-15 10:45:00+00');

-- Insert merchants with various statuses and trust scores
INSERT INTO public.merchants (id, name, email, website, business_type, contact_name, phone, address, webhook_url, api_key, webhook_secret, status, trust_score, created_at) VALUES
-- Active merchants
('660e8400-e29b-41d4-a716-************', 'TechStore Colombia', '<EMAIL>', 'https://techstore.co', 'Electronics', 'Juan Carlos Tech', '+57-************', 'Calle 93 #15-20, Bogotá', 'https://techstore.co/webhooks/alpha', 'ts_api_key_abc123', 'ts_webhook_secret_xyz789', 'active', 92, '2024-01-01 08:00:00+00'),
('660e8400-e29b-41d4-a716-************', 'Moda Urbana', '<EMAIL>', 'https://modaurbana.com', 'Fashion', 'Andrea Moda', '+57-************', 'Carrera 11 #85-32, Bogotá', 'https://modaurbana.com/webhooks/alpha', 'mu_api_key_def456', 'mu_webhook_secret_abc123', 'active', 88, '2024-01-05 10:30:00+00'),
('660e8400-e29b-41d4-a716-************', 'Casa & Hogar', '<EMAIL>', 'https://casayhogar.co', 'Home & Garden', 'Roberto Casa', '+57-************', 'Avenida 19 #104-62, Bogotá', 'https://casayhogar.co/webhooks/alpha', 'ch_api_key_ghi789', 'ch_webhook_secret_def456', 'active', 85, '2024-01-10 14:15:00+00'),
('660e8400-e29b-41d4-a716-************', 'Deportes Max', '<EMAIL>', 'https://deportesmax.com', 'Sports', 'Carlos Deportes', '+57-601-456-7890', 'Calle 63 #9-15, Bogotá', 'https://deportesmax.com/webhooks/alpha', 'dm_api_key_jkl012', 'dm_webhook_secret_ghi789', 'active', 90, '2024-01-15 09:45:00+00'),
-- Pending merchant
('660e8400-e29b-41d4-a716-************', 'Nueva Tienda', '<EMAIL>', 'https://nuevatienda.co', 'General', 'María Nueva', '+57-601-567-8901', 'Carrera 7 #45-23, Bogotá', 'https://nuevatienda.co/webhooks/alpha', 'nt_api_key_mno345', 'nt_webhook_secret_jkl012', 'pending', 50, '2024-06-15 11:30:00+00');

-- Insert KYC checks for all users
INSERT INTO public.kyc_checks (id, user_id, status, provider, provider_response, document_number, full_name, email, created_at) VALUES
-- Approved KYC checks
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 95, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low", "verification_time": "2024-01-16T11:15:00Z"}', 
 '12345678', 'María Alejandra González Ruiz', '<EMAIL>', '2024-01-16 11:15:00+00'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 88, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '23456789', 'Carlos Eduardo Rodríguez López', '<EMAIL>', '2024-01-21 10:30:00+00'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 92, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '34567890', 'Ana Lucía Martínez Torres', '<EMAIL>', '2024-02-02 09:45:00+00'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 85, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "medium"}', 
 '45678901', 'Diego Alejandro Ramírez Castillo', '<EMAIL>', '2024-02-11 12:00:00+00'),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 97, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '56789012', 'Sofía Isabel Herrera Morales', '<EMAIL>', '2024-02-16 08:30:00+00'),
-- Pending KYC
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'pending', 'Truora', 
 '{"status": "processing", "submitted_at": "2024-06-01T09:00:00Z"}', 
 '67890123', 'Luis Fernando Castro Jiménez', '<EMAIL>', '2024-06-01 09:00:00+00'),
-- Rejected KYC
('770e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440008', 'rejected', 'Truora', 
 '{"score": 45, "identity_verified": false, "document_valid": false, "risk_level": "high", "reason": "Document verification failed", "rejected_at": "2024-05-15T11:00:00Z"}', 
 '89012345', 'Usuario Rechazado Test', '<EMAIL>', '2024-05-15 11:00:00+00');

-- Insert loans with STRICT constraint adherence
INSERT INTO public.loans (id, user_id, merchant_id, amount, status, interest_rate, installments, installment_amount, due_date, product_details, merchant_data, decision_data, status_reason, completed_at, checkout_data, created_at) VALUES
-- Completed loans (installments > 0, completed_at set)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 1200.00, 'completed', 2.5, 3, 408.33, '2024-05-15 23:59:59+00', 
 '{"product_name": "iPhone 15", "category": "Electronics", "sku": "IP15-128GB", "price": 1200.00}',
 '{"order_id": "TS-2024-001", "return_url": "https://techstore.co/success", "merchant_reference": "REF-001"}',
 '{"credit_score": 750, "approval_reason": "Excellent credit history", "risk_assessment": "low", "decision_time_ms": 1250}',
 'Loan completed successfully', '2024-05-10 14:30:00+00',
 '{"checkout_session": "cs_test_123456", "payment_intent": "pi_test_789012"}', '2024-03-15 10:00:00+00'),

('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 800.00, 'completed', 3.0, 2, 412.00, '2024-04-20 23:59:59+00', 
 '{"product_name": "Nike Air Max 270", "category": "Fashion", "sku": "NAM-270-42", "price": 800.00}',
 '{"order_id": "MU-2024-002", "return_url": "https://modaurbana.com/success", "merchant_reference": "REF-002"}',
 '{"credit_score": 720, "approval_reason": "Good payment history", "risk_assessment": "low", "decision_time_ms": 980}',
 'Loan completed successfully', '2024-04-18 16:45:00+00',
 '{"checkout_session": "cs_test_234567", "payment_intent": "pi_test_890123"}', '2024-02-20 14:15:00+00'),

-- Active loans (installments > 0, no completed_at)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 1500.00, 'active', 2.8, 4, 394.50, '2024-08-15 23:59:59+00', 
 '{"product_name": "Sofá 3 Puestos Gris", "category": "Furniture", "sku": "SF-3P-GRAY", "price": 1500.00}',
 '{"order_id": "CH-2024-003", "return_url": "https://casayhogar.co/success", "merchant_reference": "REF-003"}',
 '{"credit_score": 680, "approval_reason": "Regular customer with good history", "risk_assessment": "medium", "decision_time_ms": 1450}',
 'Loan approved and active', NULL,
 '{"checkout_session": "cs_test_345678", "payment_intent": "pi_test_901234"}', '2024-06-15 09:30:00+00'),

('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 600.00, 'active', 3.5, 2, 318.50, '2024-07-30 23:59:59+00', 
 '{"product_name": "Bicicleta MTB Trek", "category": "Sports", "sku": "MTB-TREK-M", "price": 600.00}',
 '{"order_id": "DM-2024-004", "return_url": "https://deportesmax.com/success", "merchant_reference": "REF-004"}',
 '{"credit_score": 650, "approval_reason": "First-time customer approved", "risk_assessment": "medium", "decision_time_ms": 2100}',
 'Loan approved and active', NULL,
 '{"checkout_session": "cs_test_456789", "payment_intent": "pi_test_012345"}', '2024-05-30 11:20:00+00'),

-- Recent approved loan
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 450.00, 'approved', 2.2, 1, 459.90, '2024-07-22 23:59:59+00', 
 '{"product_name": "Vestido Casual de Verano", "category": "Fashion", "sku": "VC-SUMMER-S", "price": 450.00}',
 '{"order_id": "MU-2024-006", "return_url": "https://modaurbana.com/success", "merchant_reference": "REF-006"}',
 '{"credit_score": 750, "approval_reason": "Excellent payment history", "risk_assessment": "low", "decision_time_ms": 750}',
 'Recently approved loan', NULL,
 '{"checkout_session": "cs_test_678901", "payment_intent": "pi_test_234567"}', '2024-06-22 15:10:00+00'),

-- REJECTED LOAN (installments = 0, interest_rate = 0, no completed_at)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 3000.00, 'rejected', 0.0, 0, 0.00, '2024-06-20 23:59:59+00', 
 '{"product_name": "Living Room Set Complete", "category": "Furniture", "sku": "LRS-COMPLETE", "price": 3000.00}',
 '{"order_id": "CH-2024-007", "return_url": "https://casayhogar.co/declined", "merchant_reference": "REF-007"}',
 '{"credit_score": 580, "rejection_reason": "Insufficient credit limit", "risk_assessment": "high", "decision_time_ms": 450}',
 'Loan rejected due to insufficient credit limit', NULL,
 '{"checkout_session": "cs_test_789012", "payment_intent": "pi_test_345678"}', '2024-06-20 08:25:00+00'),

-- CANCELLED LOAN (installments = 0, interest_rate = 0, no completed_at)
('880e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 900.00, 'cancelled', 0.0, 0, 0.00, '2024-06-25 23:59:59+00', 
 '{"product_name": "Samsung Galaxy S24", "category": "Electronics", "sku": "SGS24-128GB", "price": 900.00}',
 '{"order_id": "TS-2024-008", "return_url": "https://techstore.co/cancelled", "merchant_reference": "REF-008"}',
 '{"credit_score": 720, "cancellation_reason": "Customer requested cancellation", "risk_assessment": "low", "decision_time_ms": 1200}',
 'Loan cancelled by customer request', NULL,
 '{"checkout_session": "cs_test_890123", "payment_intent": "pi_test_456789"}', '2024-06-25 10:15:00+00');

-- Insert repayments (only for loans with installments > 0)
INSERT INTO public.repayments (id, loan_id, user_id, amount, status, payment_method, reference_number, notes, processed_at, created_at) VALUES
-- Repayments for completed loan 1 (iPhone 15 - 3 installments)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.33, 'completed', 'PSE', 'PSE-2024-001-001', 'First installment payment - iPhone 15', '2024-03-30 10:15:00+00', '2024-03-30 10:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.33, 'completed', 'PSE', 'PSE-2024-001-002', 'Second installment payment - iPhone 15', '2024-04-30 14:20:00+00', '2024-04-30 14:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.34, 'completed', 'PSE', 'PSE-2024-001-003', 'Final installment payment - iPhone 15', '2024-05-10 14:30:00+00', '2024-05-10 14:15:00+00'),
-- Repayments for completed loan 2 (Nike Air Max - 2 installments)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 412.00, 'completed', 'Credit Card', 'CC-2024-002-001', 'First installment payment - Nike Air Max', '2024-03-20 16:10:00+00', '2024-03-20 16:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 412.00, 'completed', 'Credit Card', 'CC-2024-002-002', 'Final installment payment - Nike Air Max', '2024-04-18 16:45:00+00', '2024-04-18 16:30:00+00'),
-- Partial repayments for active loans
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 394.50, 'completed', 'Bank Transfer', 'BT-2024-003-001', 'First installment payment - Sofá 3 Puestos', '2024-06-30 12:00:00+00', '2024-06-30 11:45:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 318.50, 'completed', 'PSE', 'PSE-2024-004-001', 'First installment payment - Bicicleta MTB', '2024-06-15 09:30:00+00', '2024-06-15 09:15:00+00');

-- Insert comprehensive events for analytics
INSERT INTO public.events (id, type, user_id, metadata, created_at) VALUES
-- User registration events
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "web", "referrer": "google_ads", "device": "desktop"}', '2024-01-15 10:00:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "mobile_app", "referrer": "facebook", "device": "android"}', '2024-01-20 14:30:00+00'),
-- KYC events
('aa0e8400-e29b-41d4-a716-************', 'kyc_approved', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 95, "processing_time_minutes": 15}', '2024-01-16 11:15:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'kyc_approved', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 88, "processing_time_minutes": 22}', '2024-01-21 10:30:00+00'),
-- Loan events
('aa0e8400-e29b-41d4-a716-************', 'loan_approved', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "amount": 1200.00, "approval_time_seconds": 45}', '2024-03-15 10:01:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'loan_completed', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "total_paid": 1225.00, "days_to_complete": 56}', '2024-05-10 14:30:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'loan_rejected', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "amount": 3000.00, "rejection_reason": "insufficient_credit_limit"}', '2024-06-20 08:25:00+00'),
-- Payment events
('aa0e8400-e29b-41d4-a716-446655440008', 'payment_completed', '550e8400-e29b-41d4-a716-************', '{"amount": 408.33, "method": "PSE", "loan_id": "880e8400-e29b-41d4-a716-************", "installment": 1}', '2024-03-30 10:15:00+00');

-- =====================================================
-- FINAL SUCCESS MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🎉 Alpha BNPL database completely reset and populated successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 DATA SUMMARY:';
    RAISE NOTICE '👥 Users: 8 (5 approved, 2 pending KYC, 1 rejected)';
    RAISE NOTICE '🏪 Merchants: 5 (4 active, 1 pending)';
    RAISE NOTICE '💰 Loans: 7 (2 completed, 2 active, 1 approved, 1 rejected, 1 cancelled)';
    RAISE NOTICE '💳 Repayments: 7 (all completed)';
    RAISE NOTICE '🔍 KYC Checks: 7 (5 approved, 1 pending, 1 rejected)';
    RAISE NOTICE '📈 Events: 8 (analytics and audit trail)';
    RAISE NOTICE '';
    RAISE NOTICE '✅ CONSTRAINT VALIDATION:';
    RAISE NOTICE '• All rejected/cancelled loans have 0 installments ✓';
    RAISE NOTICE '• All active/completed loans have installments > 0 ✓';
    RAISE NOTICE '• All amounts are > 0 ✓';
    RAISE NOTICE '• All interest rates are >= 0 ✓';
    RAISE NOTICE '• Foreign key relationships maintained ✓';
    RAISE NOTICE '';
    RAISE NOTICE '🔑 TEST CREDENTIALS:';
    RAISE NOTICE '📧 Test user: <EMAIL> (approved, ₡5,000 credit)';
    RAISE NOTICE '🏪 Demo merchant API key: ts_api_key_abc123';
    RAISE NOTICE '🌐 Access dashboard at: http://localhost:3000';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your Alpha BNPL platform is ready for comprehensive testing!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 CONSTRAINT DETAILS:';
    RAISE NOTICE '• loans.installments CHECK (installments >= 0) ✓';
    RAISE NOTICE '• loans.amount CHECK (amount > 0) ✓';
    RAISE NOTICE '• loans.interest_rate CHECK (interest_rate >= 0) ✓';
    RAISE NOTICE '• All foreign keys properly configured ✓';
END $$; 