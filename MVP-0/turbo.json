{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build", "db:generate"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"outputs": []}, "test": {"dependsOn": ["db:generate"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["build"], "outputs": ["cypress/videos/**", "cypress/screenshots/**"]}, "dev": {"cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "clean": {"cache": false}, "storybook": {"cache": false, "persistent": true}}}