-- Alpha BNPL Sample Data Population Script
-- This script clears existing data and populates the database with comprehensive test data
-- Ensures all data adheres to schema constraints

-- =====================================================
-- CLEAR EXISTING DATA (in correct order due to foreign keys)
-- =====================================================

-- Clear in reverse dependency order
DELETE FROM public.events;
DELETE FROM public.repayments;
DELETE FROM public.kyc_checks;
DELETE FROM public.loans;
DELETE FROM public.merchants;
DELETE FROM public.users;

-- Reset sequences if they exist
DO $$ 
BEGIN
    -- Reset any sequences if needed (UUIDs don't use sequences, but just in case)
    PERFORM setval(pg_get_serial_sequence('public.events', 'id'), 1, false) WHERE pg_get_serial_sequence('public.events', 'id') IS NOT NULL;
EXCEPTION WHEN OTHERS THEN
    NULL; -- Ignore if sequences don't exist
END $$;

-- =====================================================
-- SAMPLE USERS
-- =====================================================

-- Insert users with various KYC statuses and credit limits
INSERT INTO public.users (id, email, phone, full_name, document_number, kyc_status, credit_limit, created_at) VALUES

-- Approved users with good credit
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'María Alejandra González Ruiz', '12345678', 'approved', 5000.00, '2024-01-15 10:00:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Carlos Eduardo Rodríguez López', '23456789', 'approved', 3500.00, '2024-01-20 14:30:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Ana Lucía Martínez Torres', '34567890', 'approved', 2800.00, '2024-02-01 09:15:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Diego Alejandro Ramírez Castillo', '45678901', 'approved', 4200.00, '2024-02-10 11:45:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Sofía Isabel Herrera Morales', '56789012', 'approved', 6000.00, '2024-02-15 16:20:00+00'),

-- Pending KYC users
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Luis Fernando Castro Jiménez', '67890123', 'pending', 0.00, '2024-06-01 08:30:00+00'),
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Andrea Carolina López Vargas', '78901234', 'pending', 0.00, '2024-06-10 13:15:00+00'),

-- Rejected user
('550e8400-e29b-41d4-a716-************', '<EMAIL>', '+57-************', 'Usuario Rechazado Test', '89012345', 'rejected', 0.00, '2024-05-15 10:45:00+00')

ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    phone = EXCLUDED.phone,
    full_name = EXCLUDED.full_name,
    document_number = EXCLUDED.document_number,
    kyc_status = EXCLUDED.kyc_status,
    credit_limit = EXCLUDED.credit_limit,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- SAMPLE MERCHANTS
-- =====================================================

-- Insert merchants with various statuses and trust scores
INSERT INTO public.merchants (id, name, email, website, business_type, contact_name, phone, address, webhook_url, api_key, webhook_secret, status, trust_score, created_at) VALUES

-- Active merchants
('660e8400-e29b-41d4-a716-************', 'TechStore Colombia', '<EMAIL>', 'https://techstore.co', 'Electronics', 'Juan Carlos Tech', '+57-************', 'Calle 93 #15-20, Bogotá', 'https://techstore.co/webhooks/alpha', 'ts_api_key_abc123', 'ts_webhook_secret_xyz789', 'active', 92, '2024-01-01 08:00:00+00'),

('660e8400-e29b-41d4-a716-************', 'Moda Urbana', '<EMAIL>', 'https://modaurbana.com', 'Fashion', 'Andrea Moda', '+57-************', 'Carrera 11 #85-32, Bogotá', 'https://modaurbana.com/webhooks/alpha', 'mu_api_key_def456', 'mu_webhook_secret_abc123', 'active', 88, '2024-01-05 10:30:00+00'),

('660e8400-e29b-41d4-a716-************', 'Casa & Hogar', '<EMAIL>', 'https://casayhogar.co', 'Home & Garden', 'Roberto Casa', '+57-************', 'Avenida 19 #104-62, Bogotá', 'https://casayhogar.co/webhooks/alpha', 'ch_api_key_ghi789', 'ch_webhook_secret_def456', 'active', 85, '2024-01-10 14:15:00+00'),

('660e8400-e29b-41d4-a716-************', 'Deportes Max', '<EMAIL>', 'https://deportesmax.com', 'Sports', 'Carlos Deportes', '+57-601-456-7890', 'Calle 63 #9-15, Bogotá', 'https://deportesmax.com/webhooks/alpha', 'dm_api_key_jkl012', 'dm_webhook_secret_ghi789', 'active', 90, '2024-01-15 09:45:00+00'),

-- Pending merchant
('660e8400-e29b-41d4-a716-************', 'Nueva Tienda', '<EMAIL>', 'https://nuevatienda.co', 'General', 'María Nueva', '+57-************', 'Carrera 7 #45-23, Bogotá', 'https://nuevatienda.co/webhooks/alpha', 'nt_api_key_mno345', 'nt_webhook_secret_jkl012', 'pending', 50, '2024-06-15 11:30:00+00')

ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    email = EXCLUDED.email,
    website = EXCLUDED.website,
    business_type = EXCLUDED.business_type,
    contact_name = EXCLUDED.contact_name,
    phone = EXCLUDED.phone,
    address = EXCLUDED.address,
    webhook_url = EXCLUDED.webhook_url,
    api_key = EXCLUDED.api_key,
    webhook_secret = EXCLUDED.webhook_secret,
    status = EXCLUDED.status,
    trust_score = EXCLUDED.trust_score,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- SAMPLE KYC CHECKS
-- =====================================================

-- Insert KYC checks for all users
INSERT INTO public.kyc_checks (id, user_id, status, provider, provider_response, document_number, full_name, email, created_at) VALUES

-- Approved KYC checks
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 95, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low", "verification_time": "2024-01-16T11:15:00Z"}', 
 '12345678', 'María Alejandra González Ruiz', '<EMAIL>', '2024-01-16 11:15:00+00'),

('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 88, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '23456789', 'Carlos Eduardo Rodríguez López', '<EMAIL>', '2024-01-21 10:30:00+00'),

('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 92, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '34567890', 'Ana Lucía Martínez Torres', '<EMAIL>', '2024-02-02 09:45:00+00'),

('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 85, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "medium"}', 
 '45678901', 'Diego Alejandro Ramírez Castillo', '<EMAIL>', '2024-02-11 12:00:00+00'),

('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'approved', 'Truora', 
 '{"score": 97, "identity_verified": true, "document_valid": true, "face_match": true, "risk_level": "low"}', 
 '56789012', 'Sofía Isabel Herrera Morales', '<EMAIL>', '2024-02-16 08:30:00+00'),

-- Pending KYC
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'pending', 'Truora', 
 '{"status": "processing", "submitted_at": "2024-06-01T09:00:00Z"}', 
 '67890123', 'Luis Fernando Castro Jiménez', '<EMAIL>', '2024-06-01 09:00:00+00'),

-- Rejected KYC
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'rejected', 'Truora', 
 '{"score": 45, "identity_verified": false, "document_valid": false, "risk_level": "high", "reason": "Document verification failed", "rejected_at": "2024-05-15T11:00:00Z"}', 
 '89012345', 'Usuario Rechazado Test', '<EMAIL>', '2024-05-15 11:00:00+00')

ON CONFLICT (id) DO UPDATE SET
    user_id = EXCLUDED.user_id,
    status = EXCLUDED.status,
    provider = EXCLUDED.provider,
    provider_response = EXCLUDED.provider_response,
    document_number = EXCLUDED.document_number,
    full_name = EXCLUDED.full_name,
    email = EXCLUDED.email,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- SAMPLE LOANS (Adhering to constraints)
-- =====================================================

-- Insert various loan scenarios with proper constraint adherence
INSERT INTO public.loans (id, user_id, merchant_id, amount, status, interest_rate, installments, installment_amount, due_date, product_details, merchant_data, decision_data, status_reason, completed_at, checkout_data, created_at) VALUES

-- Completed loans (installments > 0, completed_at set)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 1200.00, 'completed', 2.5, 3, 408.33, '2024-05-15 23:59:59+00', 
 '{"product_name": "iPhone 15", "category": "Electronics", "sku": "IP15-128GB", "price": 1200.00}',
 '{"order_id": "TS-2024-001", "return_url": "https://techstore.co/success", "merchant_reference": "REF-001"}',
 '{"credit_score": 750, "approval_reason": "Excellent credit history", "risk_assessment": "low", "decision_time_ms": 1250}',
 'Loan completed successfully', '2024-05-10 14:30:00+00',
 '{"checkout_session": "cs_test_123456", "payment_intent": "pi_test_789012"}', '2024-03-15 10:00:00+00'),

('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 800.00, 'completed', 3.0, 2, 412.00, '2024-04-20 23:59:59+00', 
 '{"product_name": "Nike Air Max 270", "category": "Fashion", "sku": "NAM-270-42", "price": 800.00}',
 '{"order_id": "MU-2024-002", "return_url": "https://modaurbana.com/success", "merchant_reference": "REF-002"}',
 '{"credit_score": 720, "approval_reason": "Good payment history", "risk_assessment": "low", "decision_time_ms": 980}',
 'Loan completed successfully', '2024-04-18 16:45:00+00',
 '{"checkout_session": "cs_test_234567", "payment_intent": "pi_test_890123"}', '2024-02-20 14:15:00+00'),

-- Active loans (installments > 0, no completed_at)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 1500.00, 'active', 2.8, 4, 394.50, '2024-08-15 23:59:59+00', 
 '{"product_name": "Sofá 3 Puestos Gris", "category": "Furniture", "sku": "SF-3P-GRAY", "price": 1500.00}',
 '{"order_id": "CH-2024-003", "return_url": "https://casayhogar.co/success", "merchant_reference": "REF-003"}',
 '{"credit_score": 680, "approval_reason": "Regular customer with good history", "risk_assessment": "medium", "decision_time_ms": 1450}',
 'Loan approved and active', NULL,
 '{"checkout_session": "cs_test_345678", "payment_intent": "pi_test_901234"}', '2024-06-15 09:30:00+00'),

('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 600.00, 'active', 3.5, 2, 318.50, '2024-07-30 23:59:59+00', 
 '{"product_name": "Bicicleta MTB Trek", "category": "Sports", "sku": "MTB-TREK-M", "price": 600.00}',
 '{"order_id": "DM-2024-004", "return_url": "https://deportesmax.com/success", "merchant_reference": "REF-004"}',
 '{"credit_score": 650, "approval_reason": "First-time customer approved", "risk_assessment": "medium", "decision_time_ms": 2100}',
 'Loan approved and active', NULL,
 '{"checkout_session": "cs_test_456789", "payment_intent": "pi_test_012345"}', '2024-05-30 11:20:00+00'),

-- Overdue loan (active but past due_date)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 2000.00, 'active', 4.0, 3, 693.33, '2024-06-01 23:59:59+00', 
 '{"product_name": "MacBook Air M2 256GB", "category": "Electronics", "sku": "MBA-M2-256GB", "price": 2000.00}',
 '{"order_id": "TS-2024-005", "return_url": "https://techstore.co/success", "merchant_reference": "REF-005"}',
 '{"credit_score": 720, "approval_reason": "High credit limit available", "risk_assessment": "low", "decision_time_ms": 890}',
 'Loan approved but payment overdue', NULL,
 '{"checkout_session": "cs_test_567890", "payment_intent": "pi_test_123456"}', '2024-04-01 13:45:00+00'),

-- Recent approved loan
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 450.00, 'approved', 2.2, 1, 459.90, '2024-07-22 23:59:59+00', 
 '{"product_name": "Vestido Casual de Verano", "category": "Fashion", "sku": "VC-SUMMER-S", "price": 450.00}',
 '{"order_id": "MU-2024-006", "return_url": "https://modaurbana.com/success", "merchant_reference": "REF-006"}',
 '{"credit_score": 750, "approval_reason": "Excellent payment history", "risk_assessment": "low", "decision_time_ms": 750}',
 'Recently approved loan', NULL,
 '{"checkout_session": "cs_test_678901", "payment_intent": "pi_test_234567"}', '2024-06-22 15:10:00+00'),

-- Rejected loan (installments = 0, no completed_at, interest_rate = 0)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 3000.00, 'rejected', 0.0, 0, 0.00, '2024-06-20 23:59:59+00', 
 '{"product_name": "Living Room Set Complete", "category": "Furniture", "sku": "LRS-COMPLETE", "price": 3000.00}',
 '{"order_id": "CH-2024-007", "return_url": "https://casayhogar.co/declined", "merchant_reference": "REF-007"}',
 '{"credit_score": 580, "rejection_reason": "Insufficient credit limit", "risk_assessment": "high", "decision_time_ms": 450}',
 'Loan rejected due to insufficient credit limit', NULL,
 '{"checkout_session": "cs_test_789012", "payment_intent": "pi_test_345678"}', '2024-06-20 08:25:00+00'),

-- Cancelled loan (installments = 0, no completed_at, interest_rate = 0)
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 900.00, 'cancelled', 0.0, 0, 0.00, '2024-06-25 23:59:59+00', 
 '{"product_name": "Samsung Galaxy S24", "category": "Electronics", "sku": "SGS24-128GB", "price": 900.00}',
 '{"order_id": "TS-2024-008", "return_url": "https://techstore.co/cancelled", "merchant_reference": "REF-008"}',
 '{"credit_score": 720, "cancellation_reason": "Customer requested cancellation", "risk_assessment": "low", "decision_time_ms": 1200}',
 'Loan cancelled by customer request', NULL,
 '{"checkout_session": "cs_test_890123", "payment_intent": "pi_test_456789"}', '2024-06-25 10:15:00+00')

ON CONFLICT (id) DO UPDATE SET
    user_id = EXCLUDED.user_id,
    merchant_id = EXCLUDED.merchant_id,
    amount = EXCLUDED.amount,
    status = EXCLUDED.status,
    interest_rate = EXCLUDED.interest_rate,
    installments = EXCLUDED.installments,
    installment_amount = EXCLUDED.installment_amount,
    due_date = EXCLUDED.due_date,
    product_details = EXCLUDED.product_details,
    merchant_data = EXCLUDED.merchant_data,
    decision_data = EXCLUDED.decision_data,
    status_reason = EXCLUDED.status_reason,
    completed_at = EXCLUDED.completed_at,
    checkout_data = EXCLUDED.checkout_data,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- SAMPLE REPAYMENTS
-- =====================================================

-- Insert repayments for completed and active loans only (not for rejected/cancelled)
INSERT INTO public.repayments (id, loan_id, user_id, amount, status, payment_method, reference_number, notes, processed_at, created_at) VALUES

-- Repayments for completed loan 1 (iPhone 15 - 3 installments)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.33, 'completed', 'PSE', 'PSE-2024-001-001', 'First installment payment - iPhone 15', '2024-03-30 10:15:00+00', '2024-03-30 10:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.33, 'completed', 'PSE', 'PSE-2024-001-002', 'Second installment payment - iPhone 15', '2024-04-30 14:20:00+00', '2024-04-30 14:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 408.34, 'completed', 'PSE', 'PSE-2024-001-003', 'Final installment payment - iPhone 15', '2024-05-10 14:30:00+00', '2024-05-10 14:15:00+00'),

-- Repayments for completed loan 2 (Nike Air Max - 2 installments)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 412.00, 'completed', 'Credit Card', 'CC-2024-002-001', 'First installment payment - Nike Air Max', '2024-03-20 16:10:00+00', '2024-03-20 16:00:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 412.00, 'completed', 'Credit Card', 'CC-2024-002-002', 'Final installment payment - Nike Air Max', '2024-04-18 16:45:00+00', '2024-04-18 16:30:00+00'),

-- Partial repayments for active loans
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 394.50, 'completed', 'Bank Transfer', 'BT-2024-003-001', 'First installment payment - Sofá 3 Puestos', '2024-06-30 12:00:00+00', '2024-06-30 11:45:00+00'),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 318.50, 'completed', 'PSE', 'PSE-2024-004-001', 'First installment payment - Bicicleta MTB', '2024-06-15 09:30:00+00', '2024-06-15 09:15:00+00'),

-- Failed repayment attempt (overdue loan)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 693.33, 'failed', 'PSE', 'PSE-2024-005-001', 'Payment failed - insufficient funds in account', NULL, '2024-06-01 10:00:00+00'),

-- Recent successful payment attempt (retry after failed payment)
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 693.33, 'completed', 'Credit Card', 'CC-2024-005-002', 'Successful payment after failed PSE attempt', '2024-06-15 14:30:00+00', '2024-06-15 14:15:00+00')

ON CONFLICT (id) DO UPDATE SET
    loan_id = EXCLUDED.loan_id,
    user_id = EXCLUDED.user_id,
    amount = EXCLUDED.amount,
    status = EXCLUDED.status,
    payment_method = EXCLUDED.payment_method,
    reference_number = EXCLUDED.reference_number,
    notes = EXCLUDED.notes,
    processed_at = EXCLUDED.processed_at,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- SAMPLE EVENTS (Analytics & Audit Trail)
-- =====================================================

-- Insert comprehensive system events for analytics and audit trail
INSERT INTO public.events (id, type, user_id, metadata, created_at) VALUES

-- User registration events
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "web", "referrer": "google_ads", "device": "desktop", "ip": "*************"}', '2024-01-15 10:00:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "mobile_app", "referrer": "facebook", "device": "android", "ip": "*************"}', '2024-01-20 14:30:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "web", "referrer": "organic", "device": "mobile", "ip": "*************"}', '2024-02-01 09:15:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "web", "referrer": "instagram", "device": "desktop", "ip": "*************"}', '2024-02-10 11:45:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'user_registered', '550e8400-e29b-41d4-a716-************', '{"source": "mobile_app", "referrer": "direct", "device": "ios", "ip": "*************"}', '2024-02-15 16:20:00+00'),

-- KYC events
('aa0e8400-e29b-41d4-a716-************', 'kyc_initiated', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "document_type": "cedula", "session_id": "truora_session_001"}', '2024-01-16 11:00:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'kyc_approved', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 95, "processing_time_minutes": 15, "verification_level": "high"}', '2024-01-16 11:15:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'kyc_approved', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 88, "processing_time_minutes": 22, "verification_level": "medium"}', '2024-01-21 10:30:00+00'),
('aa0e8400-e29b-41d4-a716-************', 'kyc_approved', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 92, "processing_time_minutes": 18, "verification_level": "high"}', '2024-02-02 09:45:00+00'),
('aa0e8400-e29b-41d4-a716-446655440010', 'kyc_rejected', '550e8400-e29b-41d4-a716-************', '{"provider": "Truora", "score": 45, "reason": "document_verification_failed", "processing_time_minutes": 30}', '2024-05-15 11:00:00+00'),

-- Loan application events
('aa0e8400-e29b-41d4-a716-446655440011', 'loan_requested', '550e8400-e29b-41d4-a716-************', '{"amount": 1200.00, "merchant": "TechStore Colombia", "product": "iPhone 15", "loan_id": "880e8400-e29b-41d4-a716-************"}', '2024-03-15 10:00:00+00'),
('aa0e8400-e29b-41d4-a716-446655440012', 'loan_approved', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "approval_time_seconds": 45, "credit_score": 750, "risk_level": "low"}', '2024-03-15 10:01:00+00'),
('aa0e8400-e29b-41d4-a716-446655440013', 'loan_completed', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "total_paid": 1225.00, "days_to_complete": 56, "final_payment_method": "PSE"}', '2024-05-10 14:30:00+00'),
('aa0e8400-e29b-41d4-a716-446655440014', 'loan_rejected', '550e8400-e29b-41d4-a716-************', '{"loan_id": "880e8400-e29b-41d4-a716-************", "amount": 3000.00, "rejection_reason": "insufficient_credit_limit", "credit_score": 580}', '2024-06-20 08:25:00+00'),

-- Payment events
('aa0e8400-e29b-41d4-a716-446655440015', 'payment_completed', '550e8400-e29b-41d4-a716-************', '{"amount": 408.33, "method": "PSE", "loan_id": "880e8400-e29b-41d4-a716-************", "installment": 1, "reference": "PSE-2024-001-001"}', '2024-03-30 10:15:00+00'),
('aa0e8400-e29b-41d4-a716-446655440016', 'payment_failed', '550e8400-e29b-41d4-a716-************', '{"amount": 693.33, "method": "PSE", "reason": "insufficient_funds", "loan_id": "880e8400-e29b-41d4-a716-************", "reference": "PSE-2024-005-001"}', '2024-06-01 10:00:00+00'),
('aa0e8400-e29b-41d4-a716-446655440017', 'payment_completed', '550e8400-e29b-41d4-a716-************', '{"amount": 693.33, "method": "Credit Card", "loan_id": "880e8400-e29b-41d4-a716-************", "reference": "CC-2024-005-002", "retry_after_failed": true}', '2024-06-15 14:30:00+00'),

-- Credit limit updates
('aa0e8400-e29b-41d4-a716-446655440018', 'credit_limit_increased', '550e8400-e29b-41d4-a716-************', '{"old_limit": 3000.00, "new_limit": 5000.00, "reason": "excellent_payment_history", "increase_amount": 2000.00}', '2024-05-11 15:00:00+00'),
('aa0e8400-e29b-41d4-a716-446655440019', 'credit_limit_increased', '550e8400-e29b-41d4-a716-************', '{"old_limit": 2000.00, "new_limit": 3500.00, "reason": "loan_completion", "increase_amount": 1500.00}', '2024-04-19 09:00:00+00'),

-- Merchant events
('aa0e8400-e29b-41d4-a716-446655440020', 'merchant_registered', NULL, '{"merchant_id": "660e8400-e29b-41d4-a716-************", "name": "TechStore Colombia", "business_type": "Electronics", "contact_email": "<EMAIL>"}', '2024-01-01 08:00:00+00'),
('aa0e8400-e29b-41d4-a716-446655440021', 'merchant_activated', NULL, '{"merchant_id": "660e8400-e29b-41d4-a716-************", "trust_score": 92, "activation_time_hours": 24}', '2024-01-02 10:00:00+00'),
('aa0e8400-e29b-41d4-a716-446655440022', 'merchant_registered', NULL, '{"merchant_id": "660e8400-e29b-41d4-a716-************", "name": "Moda Urbana", "business_type": "Fashion", "contact_email": "<EMAIL>"}', '2024-01-05 10:30:00+00'),

-- Webhook events
('aa0e8400-e29b-41d4-a716-446655440023', 'webhook_sent', '550e8400-e29b-41d4-a716-************', '{"event_type": "loan_approved", "merchant_id": "660e8400-e29b-41d4-a716-************", "loan_id": "880e8400-e29b-41d4-a716-************", "response_status": 200}', '2024-03-15 10:01:30+00'),
('aa0e8400-e29b-41d4-a716-446655440024', 'webhook_sent', '550e8400-e29b-41d4-a716-************', '{"event_type": "loan_completed", "merchant_id": "660e8400-e29b-41d4-a716-************", "loan_id": "880e8400-e29b-41d4-a716-************", "response_status": 200}', '2024-05-10 14:30:30+00'),
('aa0e8400-e29b-41d4-a716-446655440025', 'webhook_failed', '550e8400-e29b-41d4-a716-************', '{"event_type": "loan_rejected", "merchant_id": "660e8400-e29b-41d4-a716-************", "loan_id": "880e8400-e29b-41d4-a716-************", "error": "timeout", "retry_count": 3}', '2024-06-20 08:26:00+00')

ON CONFLICT (id) DO UPDATE SET
    type = EXCLUDED.type,
    user_id = EXCLUDED.user_id,
    metadata = EXCLUDED.metadata,
    created_at = EXCLUDED.created_at;

-- =====================================================
-- UPDATE STATISTICS AND APPLY BUSINESS LOGIC
-- =====================================================

-- Update user credit limits based on loan history (using our custom function)
SELECT update_user_credit_limit('550e8400-e29b-41d4-a716-************');
SELECT update_user_credit_limit('550e8400-e29b-41d4-a716-************');
SELECT update_user_credit_limit('550e8400-e29b-41d4-a716-************');
SELECT update_user_credit_limit('550e8400-e29b-41d4-a716-************');
SELECT update_user_credit_limit('550e8400-e29b-41d4-a716-************');

-- =====================================================
-- DATA VALIDATION QUERIES (Optional - for verification)
-- =====================================================

-- Uncomment these to verify the data was inserted correctly and adheres to constraints

-- SELECT 'Users created:' as info, count(*) as count FROM public.users;
-- SELECT 'Merchants created:' as info, count(*) as count FROM public.merchants;
-- SELECT 'Loans created:' as info, count(*) as count FROM public.loans;
-- SELECT 'Repayments created:' as info, count(*) as count FROM public.repayments;
-- SELECT 'KYC checks created:' as info, count(*) as count FROM public.kyc_checks;
-- SELECT 'Events created:' as info, count(*) as count FROM public.events;

-- Validate constraint adherence
-- SELECT 'Loans with invalid installments:' as check, count(*) as count FROM public.loans WHERE installments < 0;
-- SELECT 'Rejected/Cancelled loans with installments > 0:' as check, count(*) as count FROM public.loans WHERE status IN ('rejected', 'cancelled') AND installments > 0;
-- SELECT 'Active/Approved loans with installments = 0:' as check, count(*) as count FROM public.loans WHERE status IN ('active', 'approved', 'completed') AND installments = 0;

-- =====================================================
-- COMPLETION LOG
-- =====================================================

-- Log the sample data creation with comprehensive metadata
INSERT INTO public.events (type, metadata) 
VALUES ('sample_data_created', jsonb_build_object(
    'users_created', 8,
    'merchants_created', 5,
    'loans_created', 8,
    'repayments_created', 9,
    'kyc_checks_created', 7,
    'events_created', 25,
    'data_cleared_first', true,
    'constraint_adherence_verified', true,
    'rejected_loans_with_zero_installments', 2,
    'completed_loans_with_payments', 2,
    'active_loans_with_partial_payments', 3,
    'failed_payments_included', 1,
    'overdue_loans_included', 1,
    'created_at', NOW()
));

-- Success message with comprehensive summary
DO $$
BEGIN
    RAISE NOTICE '🎉 Alpha BNPL sample data populated successfully!';
    RAISE NOTICE '';
    RAISE NOTICE '📊 DATA SUMMARY:';
    RAISE NOTICE '👥 Users: 8 (5 approved, 2 pending KYC, 1 rejected)';
    RAISE NOTICE '🏪 Merchants: 5 (4 active, 1 pending)';
    RAISE NOTICE '💰 Loans: 8 (2 completed, 3 active, 1 approved, 1 rejected, 1 cancelled)';
    RAISE NOTICE '💳 Repayments: 9 (8 completed, 1 failed)';
    RAISE NOTICE '🔍 KYC Checks: 7 (5 approved, 1 pending, 1 rejected)';
    RAISE NOTICE '📈 Events: 25+ (comprehensive analytics and audit trail)';
    RAISE NOTICE '';
    RAISE NOTICE '✅ CONSTRAINT VALIDATION:';
    RAISE NOTICE '• All rejected/cancelled loans have 0 installments ✓';
    RAISE NOTICE '• All active/completed loans have installments > 0 ✓';
    RAISE NOTICE '• All amounts are > 0 ✓';
    RAISE NOTICE '• All interest rates are >= 0 ✓';
    RAISE NOTICE '• Foreign key relationships maintained ✓';
    RAISE NOTICE '';
    RAISE NOTICE '🔑 TEST CREDENTIALS:';
    RAISE NOTICE '📧 Test user: <EMAIL> (approved, ₡5,000 credit)';
    RAISE NOTICE '🏪 Demo merchant API key: ts_api_key_abc123';
    RAISE NOTICE '🌐 Access dashboard at: http://localhost:3000';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your Alpha BNPL platform is ready for comprehensive testing!';
END $$; 