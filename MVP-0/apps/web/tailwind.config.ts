import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        // Alpha Brand System
        red: {
          300: '#FF7272',
          500: '#FF3B3B', 
          700: '#E02020',
        },
        charcoal: {
          900: '#212121',
        },
        cloud: {
          50: '#FAFAFA',
        },
        // CSS Custom Properties for runtime theming
        primary: 'var(--color-red-700)',
        'primary-hover': 'var(--color-red-500)',
        'primary-light': 'var(--color-red-300)',
        text: 'var(--color-charcoal-900)',
        surface: 'var(--color-cloud-50)',
      },
      fontFamily: {
        // Primary UI font: Sora with fallbacks
        sans: ['Sora', 'Inter', 'Helvetica Neue', 'Arial', 'sans-serif'],
        // Numeric/code font: Roboto Mono
        mono: ['Roboto Mono', 'monospace'],
      },
      fontWeight: {
        normal: '400',
        semibold: '600', 
        bold: '700',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        'scale-press': 'scalePress 0.1s ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(8px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scalePress: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.97)' },
          '100%': { transform: 'scale(1)' },
        },
      },
      backdropBlur: {
        sm: '4px',
      },
      boxShadow: {
        'neon': '0 0 20px rgba(255, 59, 59, 0.3)',
        'glass': '0 8px 32px rgba(0, 0, 0, 0.1)',
      },
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
};

export default config; 