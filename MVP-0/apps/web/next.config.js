const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Transpile packages if needed
  transpilePackages: [],
  
  // Enable experimental features
  experimental: {
    // App directory is now stable in Next.js 14
    // No need to explicitly enable it
  },

  // Image optimization
  images: {
    domains: [
      'localhost',
      'alpha.com.py',
      'supabase.co',
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
    ];
  },

  // PWA Configuration
  async rewrites() {
    return [
      {
        source: '/manifest.json',
        destination: '/api/manifest',
      },
    ];
  },

  // Redirects
  async redirects() {
    return [
      // Add any redirects here
    ];
  },
};

module.exports = nextConfig; 