{"name": "@alpha/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "clean": "rm -rf .next .turbo dist coverage"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^5.6.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.38.0", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.292.0", "next": "14.0.0", "postcss": "^8.4.31", "posthog-js": "^1.88.4", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.5", "zod": "^3.22.4"}, "devDependencies": {"@storybook/addon-essentials": "^7.5.2", "@storybook/addon-interactions": "^7.5.2", "@storybook/addon-links": "^7.5.2", "@storybook/blocks": "^7.5.2", "@storybook/nextjs": "^7.5.2", "@storybook/react": "^7.5.2", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.8.9", "@types/qrcode": "^1.5.5", "@types/react": "^18.2.33", "@types/react-dom": "^18.2.14", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "cypress": "^13.4.0", "eslint": "^8.52.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-next": "14.0.0", "eslint-plugin-tailwindcss": "^3.13.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6", "prisma": "^5.6.0", "storybook": "^7.5.2", "ts-jest": "^29.1.1", "typescript": "^5.2.2"}}