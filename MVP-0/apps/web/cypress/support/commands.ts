// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

/// <reference types="cypress" />

// Custom command to get element by data-testid
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`);
});

// Custom command to login user
Cypress.Commands.add('login', (email: string) => {
  cy.window().then((win) => {
    // Mock authentication by setting session
    win.localStorage.setItem('supabase.auth.token', JSON.stringify({
      access_token: 'mock-token',
      user: { 
        id: 'demo-user-id',
        email: email 
      }
    }));
  });
  cy.reload();
});

// Custom command to create checkout
Cypress.Commands.add('createCheckout', (amount: number, description?: string) => {
  cy.getByTestId('start-checkout-button').click();
  cy.getByTestId('checkout-amount-input').type(amount.toString());
  
  if (description) {
    cy.getByTestId('checkout-description-input').type(description);
  }
  
  cy.getByTestId('checkout-submit-button').click();
});

// Prevent TypeScript from reading file as legacy script
export {}; 