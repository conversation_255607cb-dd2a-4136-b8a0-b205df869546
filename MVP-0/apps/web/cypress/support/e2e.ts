// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import Cypress types
/// <reference types="cypress" />

// Extend Cypress namespace with custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to authenticate user
       * @example cy.login('<EMAIL>')
       */
      login(email: string): Chainable<void>;
      
      /**
       * Custom command to create a checkout
       * @example cy.createCheckout(150000, 'Test purchase')
       */
      createCheckout(amount: number, description?: string): Chainable<void>;
      
      /**
       * Custom command to wait for element with data-testid
       * @example cy.getByTestId('submit-button')
       */
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
    }
  }
} 