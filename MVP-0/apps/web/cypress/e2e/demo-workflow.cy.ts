/// <reference types="cypress" />

describe('Alpha BNPL Demo Workflow', () => {
  beforeEach(() => {
    // Visit the homepage
    cy.visit('/');
  });

  it('should complete the full Alpha BNPL demo workflow', () => {
    // Step 1: Homepage loads correctly
    cy.get('[data-testid="homepage"]').should('be.visible');
    cy.contains('Alpha BNPL').should('be.visible');
    
    // Step 2: Start magic-link authentication
    cy.get('[data-testid="auth-email-input"]').type('<EMAIL>');
    cy.get('[data-testid="auth-submit-button"]').click();
    
    // Verify magic link sent message
    cy.contains('Te enviamos un enlace mágico').should('be.visible');
    
    // Step 3: Simulate magic link click (in real scenario, user would click email link)
    // For demo purposes, we'll directly navigate to the authenticated state
    cy.window().then((win) => {
      // Mock authentication by setting session
      win.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: { 
          id: 'demo-user-id',
          email: '<EMAIL>' 
        }
      }));
    });
    
    cy.reload();
    
    // Step 4: Verify user is authenticated and can access dashboard
    cy.get('[data-testid="user-dashboard"]').should('be.visible');
    cy.contains('Bienvenido').should('be.visible');
    
    // Step 5: Initiate checkout process
    cy.get('[data-testid="start-checkout-button"]').click();
    
    // Step 6: Fill checkout form
    cy.get('[data-testid="checkout-amount-input"]').type('150000'); // 150,000 PYG
    cy.get('[data-testid="checkout-description-input"]').type('Demo purchase');
    cy.get('[data-testid="checkout-submit-button"]').click();
    
    // Step 7: Verify KYC check is initiated
    cy.contains('Verificando identidad').should('be.visible');
    
    // Wait for mock KYC to complete
    cy.wait(2000);
    
    // Step 8: Verify QR code is generated and displayed
    cy.get('[data-testid="qr-code-container"]').should('be.visible');
    cy.get('[data-testid="qr-code-image"]').should('be.visible');
    cy.contains('150,000 PYG').should('be.visible');
    cy.contains('Escanea con tu app de banco').should('be.visible');
    
    // Step 9: Verify checkout details
    cy.get('[data-testid="checkout-id"]').should('contain', 'CHK_');
    cy.get('[data-testid="checkout-amount"]').should('contain', '150,000');
    
    // Step 10: Verify payment instructions are shown
    cy.contains('Cómo pagar:').should('be.visible');
    cy.contains('Abre tu app bancaria').should('be.visible');
    cy.contains('SIPAP').should('be.visible');
    
    // Step 11: Simulate payment completion (mock webhook)
    cy.request('POST', '/api/webhooks/payment', {
      checkout_id: 'mock-checkout-id',
      status: 'completed',
      amount: 150000
    }).then((response) => {
      expect(response.status).to.eq(200);
    });
    
    // Step 12: Verify loan activation
    cy.get('[data-testid="payment-success"]').should('be.visible');
    cy.contains('¡Pago recibido!').should('be.visible');
    cy.contains('Tu préstamo ha sido activado').should('be.visible');
    
    // Step 13: Navigate to loan dashboard
    cy.get('[data-testid="view-loan-button"]').click();
    
    // Step 14: Verify loan details
    cy.get('[data-testid="loan-dashboard"]').should('be.visible');
    cy.contains('Préstamo Activo').should('be.visible');
    cy.get('[data-testid="loan-amount"]').should('contain', '150,000');
    cy.get('[data-testid="loan-status"]').should('contain', 'ACTIVE');
    
    // Step 15: Verify repayment schedule
    cy.get('[data-testid="repayment-schedule"]').should('be.visible');
    cy.get('[data-testid="next-payment-date"]').should('be.visible');
    cy.get('[data-testid="next-payment-amount"]').should('be.visible');
  });

  it('should handle authentication errors gracefully', () => {
    // Test invalid email
    cy.get('[data-testid="auth-email-input"]').type('invalid-email');
    cy.get('[data-testid="auth-submit-button"]').click();
    
    cy.contains('Email inválido').should('be.visible');
    
    // Test network error simulation
    cy.intercept('POST', '/auth/v1/otp', { forceNetworkError: true });
    
    cy.get('[data-testid="auth-email-input"]').clear().type('<EMAIL>');
    cy.get('[data-testid="auth-submit-button"]').click();
    
    cy.contains('Error de conexión').should('be.visible');
  });

  it('should handle KYC failures', () => {
    // Mock authentication
    cy.window().then((win) => {
      win.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: { 
          id: 'demo-user-id',
          email: '<EMAIL>' 
        }
      }));
    });
    
    cy.reload();
    
    // Mock KYC failure
    cy.intercept('POST', '/api/kyc/mock', {
      statusCode: 400,
      body: { error: 'KYC verification failed' }
    });
    
    // Start checkout
    cy.get('[data-testid="start-checkout-button"]').click();
    cy.get('[data-testid="checkout-amount-input"]').type('150000');
    cy.get('[data-testid="checkout-submit-button"]').click();
    
    // Verify KYC failure handling
    cy.contains('Verificación fallida').should('be.visible');
    cy.contains('Por favor, intenta nuevamente').should('be.visible');
  });

  it('should validate checkout amounts', () => {
    // Mock authentication
    cy.window().then((win) => {
      win.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: { 
          id: 'demo-user-id',
          email: '<EMAIL>' 
        }
      }));
    });
    
    cy.reload();
    
    // Test minimum amount validation
    cy.get('[data-testid="start-checkout-button"]').click();
    cy.get('[data-testid="checkout-amount-input"]').type('5000'); // Below minimum
    cy.get('[data-testid="checkout-submit-button"]').click();
    
    cy.contains('Monto mínimo').should('be.visible');
    
    // Test maximum amount validation
    cy.get('[data-testid="checkout-amount-input"]').clear().type('60000000'); // Above maximum
    cy.get('[data-testid="checkout-submit-button"]').click();
    
    cy.contains('Monto máximo').should('be.visible');
  });

  it('should track analytics events', () => {
    // Mock PostHog by extending the window object
    cy.window().then((win: any) => {
      win.posthog = {
        capture: cy.stub().as('posthogCapture'),
        identify: cy.stub().as('posthogIdentify')
      };
    });
    
    // Complete authentication
    cy.get('[data-testid="auth-email-input"]').type('<EMAIL>');
    cy.get('[data-testid="auth-submit-button"]').click();
    
    // Verify auth events are tracked
    cy.get('@posthogCapture').should('have.been.calledWith', 'login_started');
    
    // Mock successful auth and continue
    cy.window().then((win) => {
      win.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        user: { 
          id: 'demo-user-id',
          email: '<EMAIL>' 
        }
      }));
    });
    
    cy.reload();
    
    // Start checkout and verify tracking
    cy.get('[data-testid="start-checkout-button"]').click();
    cy.get('[data-testid="checkout-amount-input"]').type('150000');
    cy.get('[data-testid="checkout-submit-button"]').click();
    
    cy.get('@posthogCapture').should('have.been.calledWith', 'checkout_started');
    
    // Wait for QR generation and verify QR viewed event
    cy.get('[data-testid="qr-code-container"]', { timeout: 5000 }).should('be.visible');
    cy.get('@posthogCapture').should('have.been.calledWith', 'qr_viewed');
  });
}); 