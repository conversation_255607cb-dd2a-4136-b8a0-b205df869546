{"extends": ["next/core-web-vitals"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "tailwindcss"], "rules": {"no-trailing-spaces": "off", "eol-last": "off", "react/require-default-props": "off", "react/function-component-definition": "off", "react/button-has-type": "off", "react/default-props-match-prop-types": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-shadow": "warn", "@typescript-eslint/no-var-requires": "off", "global-require": "off", "no-console": "warn", "no-nested-ternary": "warn", "object-curly-newline": "off", "react/jsx-no-constructed-context-values": "warn", "tailwindcss/classnames-order": "off", "tailwindcss/no-custom-classname": "off", "import/no-extraneous-dependencies": "off"}, "settings": {"tailwindcss": {"config": "./tailwind.config.ts"}}}