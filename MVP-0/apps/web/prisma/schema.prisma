// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum KycStatus {
  PENDING
  APPROVED
  REJECTED
  EXPIRED
}

enum LoanStatus {
  PENDING
  APPROVED
  ACTIVE
  COMPLETED
  DEFAULTED
  CANCELLED
}

enum RepaymentStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum EventType {
  USER_SIGNUP
  KYC_SUBMITTED
  KYC_APPROVED
  KYC_REJECTED
  CHECKOUT_STARTED
  QR_VIEWED
  PAYMENT_RECEIVED
  LOAN_ACTIVATED
  REPAYMENT_MADE
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  phone             String?
  firstName         String?
  lastName          String?
  dateOfBirth       DateTime?
  nationalId        String?   @unique
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relations
  kycChecks         KycCheck[]
  loans             Loan[]
  events            Event[]
  
  @@map("users")
}

model KycCheck {
  id                String     @id @default(cuid())
  userId            String
  status            KycStatus  @default(PENDING)
  score             Int?
  decision          String?
  referenceId       String?    @unique
  equifaxResponse   Json?
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  
  // Relations
  user              User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("kyc_checks")
}

model Loan {
  id                String       @id @default(cuid())
  userId            String
  status            LoanStatus   @default(PENDING)
  principalAmount   Decimal      @db.Decimal(10, 2)
  interestRate      Decimal      @db.Decimal(5, 4)
  termDays          Int
  installments      Int
  outstandingBalance Decimal     @db.Decimal(10, 2)
  checkoutId        String?      @unique
  qrData            String?
  activatedAt       DateTime?
  dueDate           DateTime?
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt
  
  // Relations
  user              User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  repayments        Repayment[]
  
  @@map("loans")
}

model Repayment {
  id                String          @id @default(cuid())
  loanId            String
  status            RepaymentStatus @default(PENDING)
  amount            Decimal         @db.Decimal(10, 2)
  dueDate           DateTime
  paidDate          DateTime?
  transactionId     String?         @unique
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  
  // Relations
  loan              Loan            @relation(fields: [loanId], references: [id], onDelete: Cascade)
  
  @@map("repayments")
}

model Event {
  id                String    @id @default(cuid())
  userId            String?
  type              EventType
  properties        Json?
  sessionId         String?
  ipAddress         String?
  userAgent         String?
  createdAt         DateTime  @default(now())
  
  // Relations
  user              User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  
  @@map("events")
} 