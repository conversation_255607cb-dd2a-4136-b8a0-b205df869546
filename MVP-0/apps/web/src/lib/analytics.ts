import { PostHog } from 'posthog-js';

// PostHog client instance
let posthog: PostHog | null = null;

// Initialize PostHog (client-side only)
export function initPostHog(): PostHog | null {
  if (typeof window !== 'undefined' && !posthog) {
    // Dynamic import for client-side only
    const { PostHog: PostHogClass } = require('posthog-js');
    posthog = new PostHogClass(
      process.env.NEXT_PUBLIC_POSTHOG_KEY,
      {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
        loaded: (ph: PostHog) => {
          if (process.env.NODE_ENV === 'development') {
            ph.debug();
          }
        },
      },
    );
  }
  return posthog;
}

// Get PostHog instance
export function getPostHog(): PostHog | null {
  return posthog || initPostHog();
}

// Track QR code viewed
export function trackQRViewed(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('qr_viewed', properties);
  }
}

// Track authentication events
export function trackAuthEvent(event: string, properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture(`auth_${event}`, properties);
  }
}

// Track checkout events
export function trackCheckoutCreated(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('checkout_created', properties);
  }
}

export function trackCheckoutStarted(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('checkout_started', properties);
  }
}

export function trackCheckoutCompleted(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('checkout_completed', properties);
  }
}

// Track loan events
export function trackLoanApplied(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('loan_applied', properties);
  }
}

export function trackLoanApproved(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('loan_approved', properties);
  }
}

export function trackLoanRejected(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('loan_rejected', properties);
  }
}

export function trackKYCCompleted(properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('kyc_completed', properties);
  }
}

// Track errors
export function trackError(event: string, properties: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture(`error_${event}`, {
      ...properties,
      timestamp: new Date().toISOString(),
    });
  }
}

// Track page views
export function trackPageView(path: string, properties?: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture('$pageview', {
      $current_url: path,
      ...properties,
    });
  }
}

// Identify user
export function identifyUser(userId: string, properties?: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.identify(userId, properties);
  }
}

// Reset user (logout)
export function resetUser(): void {
  const ph = getPostHog();
  if (ph) {
    ph.reset();
  }
}

// Custom event tracking
export function trackCustomEvent(eventName: string, properties?: Record<string, unknown>): void {
  const ph = getPostHog();
  if (ph) {
    ph.capture(eventName, {
      timestamp: new Date().toISOString(),
      ...properties,
    });
  }
}

// Alpha BNPL specific events
export const AlphaEvents = {
  // User events
  USER_SIGNUP: 'user_signup',
  USER_LOGIN: 'user_login',
  
  // KYC events
  KYC_STARTED: 'kyc_started',
  KYC_COMPLETED: 'kyc_completed',
  
  // Checkout events
  CHECKOUT_STARTED: 'checkout_started',
  QR_VIEWED: 'qr_viewed',
  QR_SCANNED: 'qr_scanned',
  
  // Payment events
  PAYMENT_RECEIVED: 'payment_received',
  LOAN_ACTIVATED: 'loan_activated',
  REPAYMENT_MADE: 'repayment_made',
  
  // Error events
  ERROR_OCCURRED: 'error_occurred',
} as const;

// Helper functions for common Alpha events
export function trackSignup(userId: string, method: string = 'email') {
  trackCustomEvent(AlphaEvents.USER_SIGNUP, { method });
  identifyUser(userId, { signup_method: method });
}

export function trackPaymentReceived(checkoutId: string, amount: number, loanId: string) {
  trackCustomEvent(AlphaEvents.PAYMENT_RECEIVED, {
    checkout_id: checkoutId,
    amount,
    loan_id: loanId,
  });
} 