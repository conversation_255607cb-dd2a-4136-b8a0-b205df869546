import QRCode from 'qrcode';

// SIPAP QR Data interface
export interface SIPAPQRData {
  amount: number;
  reference: string;
  cbu: string;
  description?: string;
}

// Alpha QR generation options
interface AlphaQROptions {
  width?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
}

// Default QR options with Alpha branding
const DEFAULT_QR_OPTIONS: AlphaQROptions = {
  width: 256,
  margin: 2,
  color: {
    dark: '#212121',
    light: '#FAFAFA',
  },
};

// Generate SIPAP QR code data string
export function generateSIPAPQR(data: SIPAPQRData): string {
  const {
    amount,
    reference,
    cbu,
    description = 'Alpha BNPL Purchase',
  } = data;

  // SIPAP QR format (simplified for demo)
  const qrString = [
    'SIPAP',
    `AMT:${amount}`,
    `REF:${reference}`,
    `CBU:${cbu}`,
    `DESC:${description}`,
    `TS:${Date.now()}`,
  ].join('|');

  return qrString;
}

// Generate QR code as data URL
export async function generateQRDataURL(
  data: string,
  options: AlphaQROptions = {},
): Promise<string> {
  const qrOptions = { ...DEFAULT_QR_OPTIONS, ...options };

  try {
    const dataURL = await QRCode.toDataURL(data, {
      width: qrOptions.width,
      margin: qrOptions.margin,
      color: qrOptions.color,
    });
    return dataURL;
  } catch (error) {
    throw new Error(`Failed to generate QR code: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Generate QR code as SVG
export async function generateQRSVG(
  data: string,
  options: AlphaQROptions = {},
): Promise<string> {
  const qrOptions = { ...DEFAULT_QR_OPTIONS, ...options };

  try {
    const svg = await QRCode.toString(data, {
      type: 'svg',
      width: qrOptions.width,
      margin: qrOptions.margin,
      color: qrOptions.color,
    });
    return svg;
  } catch (error) {
    throw new Error(`Failed to generate QR SVG: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Generate Alpha-branded QR code for SIPAP payments
export async function generateAlphaQR(
  sipapData: Omit<SIPAPQRData, 'cbu'>,
  options: AlphaQROptions = {},
): Promise<string> {
  // Use Alpha's CBU (from environment or default)
  const alphaCBU = process.env.NEXT_PUBLIC_ALPHA_CBU || '0000000000000000000000';

  const fullSipapData: SIPAPQRData = {
    ...sipapData,
    cbu: alphaCBU,
  };

  // Generate SIPAP QR string
  const qrString = generateSIPAPQR(fullSipapData);

  // Generate data URL
  return generateQRDataURL(qrString, options);
}

// Validate SIPAP QR data
export function validateSIPAPData(data: SIPAPQRData): boolean {
  const { amount, reference, cbu } = data;

  if (!amount || amount <= 0) {
    return false;
  }

  if (!reference || reference.length < 3) {
    return false;
  }

  if (!cbu || cbu.length !== 22) {
    return false;
  }

  return true;
} 