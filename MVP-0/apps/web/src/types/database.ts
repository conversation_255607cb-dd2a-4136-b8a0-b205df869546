export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          phone?: string;
          full_name?: string;
          document_number?: string;
          kyc_status: 'pending' | 'approved' | 'rejected';
          credit_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          phone?: string;
          full_name?: string;
          document_number?: string;
          kyc_status?: 'pending' | 'approved' | 'rejected';
          credit_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          phone?: string;
          full_name?: string;
          document_number?: string;
          kyc_status?: 'pending' | 'approved' | 'rejected';
          credit_limit?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      merchants: {
        Row: {
          id: string;
          name: string;
          email: string;
          website: string;
          business_type: string;
          contact_name: string;
          phone?: string;
          address?: string;
          webhook_url?: string;
          api_key: string;
          webhook_secret: string;
          status: 'pending' | 'active' | 'suspended';
          trust_score: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          email: string;
          website: string;
          business_type: string;
          contact_name: string;
          phone?: string;
          address?: string;
          webhook_url?: string;
          api_key: string;
          webhook_secret: string;
          status?: 'pending' | 'active' | 'suspended';
          trust_score?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          email?: string;
          website?: string;
          business_type?: string;
          contact_name?: string;
          phone?: string;
          address?: string;
          webhook_url?: string;
          api_key?: string;
          webhook_secret?: string;
          status?: 'pending' | 'active' | 'suspended';
          trust_score?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      loans: {
        Row: {
          id: string;
          user_id: string;
          merchant_id?: string;
          amount: number;
          status: 'pending' | 'approved' | 'active' | 'completed' | 'defaulted' | 'rejected' | 'cancelled';
          interest_rate?: number;
          installments?: number;
          installment_amount?: number;
          due_date: string;
          product_details?: Record<string, unknown>;
          merchant_data?: Record<string, unknown>;
          decision_data?: Record<string, unknown>;
          status_reason?: string;
          completed_at?: string;
          checkout_data?: Record<string, unknown>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          merchant_id?: string;
          amount: number;
          status?: 'pending' | 'approved' | 'active' | 'completed' | 'defaulted' | 'rejected' | 'cancelled';
          interest_rate?: number;
          installments?: number;
          installment_amount?: number;
          due_date: string;
          product_details?: Record<string, unknown>;
          merchant_data?: Record<string, unknown>;
          decision_data?: Record<string, unknown>;
          status_reason?: string;
          completed_at?: string;
          checkout_data?: Record<string, unknown>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          merchant_id?: string;
          amount?: number;
          status?: 'pending' | 'approved' | 'active' | 'completed' | 'defaulted' | 'rejected' | 'cancelled';
          interest_rate?: number;
          installments?: number;
          installment_amount?: number;
          due_date?: string;
          product_details?: Record<string, unknown>;
          merchant_data?: Record<string, unknown>;
          decision_data?: Record<string, unknown>;
          status_reason?: string;
          completed_at?: string;
          checkout_data?: Record<string, unknown>;
          created_at?: string;
          updated_at?: string;
        };
      };
      repayments: {
        Row: {
          id: string;
          loan_id: string;
          user_id: string;
          amount: number;
          status: 'pending' | 'completed' | 'failed';
          payment_method: string;
          reference_number?: string;
          notes?: string;
          processed_at?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          loan_id: string;
          user_id: string;
          amount: number;
          status?: 'pending' | 'completed' | 'failed';
          payment_method: string;
          reference_number?: string;
          notes?: string;
          processed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          loan_id?: string;
          user_id?: string;
          amount?: number;
          status?: 'pending' | 'completed' | 'failed';
          payment_method?: string;
          reference_number?: string;
          notes?: string;
          processed_at?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      kyc_checks: {
        Row: {
          id: string;
          user_id: string;
          status: 'pending' | 'approved' | 'rejected';
          provider: string;
          provider_response?: Record<string, unknown>;
          document_number?: string;
          full_name?: string;
          email?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          status?: 'pending' | 'approved' | 'rejected';
          provider: string;
          provider_response?: Record<string, unknown>;
          document_number?: string;
          full_name?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          status?: 'pending' | 'approved' | 'rejected';
          provider?: string;
          provider_response?: Record<string, unknown>;
          document_number?: string;
          full_name?: string;
          email?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      events: {
        Row: {
          id: string;
          type: string;
          user_id?: string;
          metadata?: Record<string, unknown>;
          created_at: string;
        };
        Insert: {
          id?: string;
          type: string;
          user_id?: string;
          metadata?: Record<string, unknown>;
          created_at?: string;
        };
        Update: {
          id?: string;
          type?: string;
          user_id?: string;
          metadata?: Record<string, unknown>;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
} 