import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackLoanApproved, trackLoanRejected, trackError, trackCustomEvent } from '@/lib/analytics';

// Merchant API endpoint for loan requests
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      merchantId, 
      merchantApiKey, 
      customerEmail, 
      amount, 
      productDetails, 
      returnUrl, 
      webhookUrl 
    } = body;

    // Validate required fields
    if (!merchantId || !merchantApiKey || !customerEmail || !amount) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'merchantId, merchantApiKey, customerEmail, and amount are required' 
        },
        { status: 400 }
      );
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Amount must be a positive number' 
        },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Verify merchant credentials
    const { data: merchant, error: merchantError } = await supabase
      .from('merchants')
      .select('*')
      .eq('id', merchantId)
      .eq('api_key', merchantApiKey)
      .eq('status', 'active')
      .single();

    if (merchantError || !merchant) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid merchant credentials' 
        },
        { status: 401 }
      );
    }

    // Find or create customer
    let customer;
    const { data: existingCustomer } = await supabase
      .from('users')
      .select('*')
      .eq('email', customerEmail)
      .single();

    if (existingCustomer) {
      customer = existingCustomer;
    } else {
      // Create new customer account
      const { data: newCustomer, error: customerError } = await supabase
        .from('users')
        .insert({
          email: customerEmail,
          kyc_status: 'pending',
          credit_limit: 0,
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (customerError) {
        trackError('customer_creation_failed', {
          error: customerError.message,
          merchantId,
          customerEmail,
        });
        return NextResponse.json(
          { 
            success: false, 
            error: 'Failed to create customer account' 
          },
          { status: 500 }
        );
      }
      customer = newCustomer;
    }

    // Generate unique loan ID
    const loanId = `loan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Instant loan decision logic
    const loanDecision = await processInstantLoanDecision(customer, amount, merchant);

    // Create loan record
    const loanData = {
      id: loanId,
      user_id: customer.id,
      merchant_id: merchantId,
      amount,
      status: loanDecision.approved ? 'approved' : 'rejected',
      interest_rate: loanDecision.interestRate,
      installments: loanDecision.installments,
      installment_amount: loanDecision.installmentAmount,
      due_date: loanDecision.dueDate,
      product_details: productDetails,
      merchant_data: {
        return_url: returnUrl,
        webhook_url: webhookUrl,
        merchant_name: merchant.name,
      },
      decision_data: {
        score: loanDecision.score,
        reasons: loanDecision.reasons,
        risk_level: loanDecision.riskLevel,
      },
      created_at: new Date().toISOString(),
    };

    const { data: loan, error: loanError } = await supabase
      .from('loans')
      .insert(loanData)
      .select()
      .single();

    if (loanError) {
      trackError('loan_creation_failed', {
        error: loanError.message,
        merchantId,
        customerEmail,
        amount,
      });
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to create loan record' 
        },
        { status: 500 }
      );
    }

    // Log event
    await supabase.from('events').insert({
      type: loanDecision.approved ? 'loan_approved' : 'loan_rejected',
      user_id: customer.id,
      metadata: {
        loan_id: loanId,
        merchant_id: merchantId,
        amount,
        decision_score: loanDecision.score,
      },
    });

    // Track analytics
    if (loanDecision.approved) {
      trackLoanApproved({
        loanId,
        userId: customer.id,
        merchantId,
        amount,
        score: loanDecision.score,
      });
    } else {
      trackLoanRejected({
        loanId,
        userId: customer.id,
        merchantId,
        amount,
        score: loanDecision.score,
        reasons: loanDecision.reasons,
      });
    }

    trackCustomEvent('loan_requested', {
      loanId,
      userId: customer.id,
      merchantId,
      amount,
    });

    // Send webhook notification to merchant if provided
    if (webhookUrl) {
      try {
        await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Alpha-Signature': generateWebhookSignature(loanData, merchant.webhook_secret),
          },
          body: JSON.stringify({
            event: 'loan_decision',
            loan_id: loanId,
            approved: loanDecision.approved,
            amount,
            customer_email: customerEmail,
            timestamp: new Date().toISOString(),
          }),
        });
      } catch (webhookError) {
        // Log webhook error but don't fail the request
        trackError('webhook_failed', {
          error: webhookError instanceof Error ? webhookError.message : 'Unknown error',
          merchantId,
          loanId,
        });
      }
    }

    return NextResponse.json({
      success: true,
      approved: loanDecision.approved,
      loan_id: loanId,
      amount,
      customer_email: customerEmail,
      ...(loanDecision.approved && {
        installments: loanDecision.installments,
        installment_amount: loanDecision.installmentAmount,
        interest_rate: loanDecision.interestRate,
        due_date: loanDecision.dueDate,
      }),
      ...(returnUrl && { return_url: returnUrl }),
      message: loanDecision.approved 
        ? 'Loan approved successfully' 
        : 'Loan application was not approved',
      reasons: loanDecision.reasons,
    });

  } catch (error) {
    trackError('merchant_api_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Instant loan decision algorithm
async function processInstantLoanDecision(customer: any, amount: number, merchant: any) {
  // Base score calculation
  let score = 500; // Base score

  // KYC status impact
  if (customer.kyc_status === 'approved') {
    score += 200;
  } else if (customer.kyc_status === 'pending') {
    score += 50;
  }

  // Credit limit consideration
  if (customer.credit_limit > 0) {
    score += Math.min(150, (customer.credit_limit / amount) * 50);
  }

  // Account age (if available)
  if (customer.created_at) {
    const accountAge = Date.now() - new Date(customer.created_at).getTime();
    const daysOld = accountAge / (1000 * 60 * 60 * 24);
    if (daysOld > 30) {
      score += Math.min(100, daysOld / 3);
    }
  }

  // Amount risk assessment
  if (amount > 1000000) { // > 10,000 PYG
    score -= 50;
  }
  if (amount > 5000000) { // > 50,000 PYG
    score -= 100;
  }

  // Merchant trust score
  if (merchant.trust_score) {
    score += merchant.trust_score * 0.5;
  }

  // Decision logic
  const approved = score >= 600;
  const riskLevel = score >= 750 ? 'low' : score >= 650 ? 'medium' : score >= 550 ? 'high' : 'very_high';
  
  // Interest rate based on risk
  const interestRate = score >= 750 ? 0.15 : score >= 700 ? 0.18 : score >= 650 ? 0.22 : 0.25;
  
  // Installment calculation (default 4 installments)
  const installments = 4;
  const totalWithInterest = amount * (1 + interestRate);
  const installmentAmount = Math.ceil(totalWithInterest / installments);
  
  // Due date (30 days from now)
  const dueDate = new Date();
  dueDate.setDate(dueDate.getDate() + 30);

  const reasons = [];
  if (approved) {
    reasons.push('Credit evaluation passed');
    if (customer.kyc_status === 'approved') reasons.push('Identity verified');
    if (customer.credit_limit > 0) reasons.push('Existing credit limit');
  } else {
    if (customer.kyc_status !== 'approved') reasons.push('Identity verification required');
    if (amount > customer.credit_limit) reasons.push('Amount exceeds credit limit');
    if (score < 600) reasons.push('Credit score below minimum threshold');
  }

  return {
    approved,
    score,
    riskLevel,
    interestRate,
    installments,
    installmentAmount,
    dueDate: dueDate.toISOString(),
    reasons,
  };
}

// Generate webhook signature for security
function generateWebhookSignature(data: any, secret: string): string {
  // Simple signature generation (in production, use proper HMAC)
  const payload = JSON.stringify(data);
  return Buffer.from(`${secret}:${payload}`).toString('base64');
} 