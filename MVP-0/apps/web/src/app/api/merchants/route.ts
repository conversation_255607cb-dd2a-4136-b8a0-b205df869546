import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackError, trackCustomEvent } from '@/lib/analytics';
import crypto from 'crypto';

// Get all merchants (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');

    const supabase = supabaseAdmin.client;

    let query = supabase
      .from('merchants')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (status) {
      query = query.eq('status', status);
    }

    const { data: merchants, error: merchantsError } = await query;

    if (merchantsError) {
      trackError('merchants_fetch_error', {
        error: merchantsError.message,
      });

      return NextResponse.json(
        { error: 'Failed to fetch merchants' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      merchants: merchants || [],
      count: merchants?.length || 0,
    });

  } catch (error) {
    trackError('merchants_api_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Register a new merchant
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      name, 
      email, 
      website, 
      business_type, 
      contact_name, 
      phone, 
      address,
      webhook_url 
    } = body;

    // Validate required fields
    if (!name || !email || !website || !contact_name) {
      return NextResponse.json(
        { error: 'name, email, website, and contact_name are required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate website URL
    try {
      new URL(website);
    } catch {
      return NextResponse.json(
        { error: 'Invalid website URL' },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Check if merchant already exists
    const { data: existingMerchant } = await supabase
      .from('merchants')
      .select('id')
      .eq('email', email)
      .single();

    if (existingMerchant) {
      return NextResponse.json(
        { error: 'Merchant with this email already exists' },
        { status: 409 }
      );
    }

    // Generate API credentials
    const merchantId = `merchant_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const apiKey = generateApiKey();
    const webhookSecret = generateWebhookSecret();

    // Create merchant record
    const merchantData = {
      id: merchantId,
      name,
      email,
      website,
      business_type: business_type || 'ecommerce',
      contact_name,
      phone,
      address,
      webhook_url,
      api_key: apiKey,
      webhook_secret: webhookSecret,
      status: 'pending', // Requires approval
      trust_score: 50, // Default trust score
      created_at: new Date().toISOString(),
    };

    const { data: merchant, error: merchantError } = await supabase
      .from('merchants')
      .insert(merchantData)
      .select()
      .single();

    if (merchantError) {
      trackError('merchant_creation_failed', {
        error: merchantError.message,
        email,
        name,
      });

      return NextResponse.json(
        { error: 'Failed to create merchant account' },
        { status: 500 }
      );
    }

    // Log merchant registration event
    await supabase.from('events').insert({
      type: 'merchant_registered',
      metadata: {
        merchant_id: merchantId,
        name,
        email,
        website,
      },
    });

    // Track analytics
    trackCustomEvent('merchant_registered', {
      merchantId,
      name,
      email,
      website,
      businessType: business_type,
    });

    // Return merchant data (without sensitive information)
    return NextResponse.json({
      success: true,
      merchant: {
        id: merchant.id,
        name: merchant.name,
        email: merchant.email,
        website: merchant.website,
        business_type: merchant.business_type,
        status: merchant.status,
        api_key: apiKey, // Only shown once during registration
        webhook_secret: webhookSecret, // Only shown once during registration
        created_at: merchant.created_at,
      },
      message: 'Merchant registered successfully. Please save your API credentials as they will not be shown again.',
    });

  } catch (error) {
    trackError('merchant_registration_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Generate secure API key
function generateApiKey(): string {
  const prefix = 'alpha_';
  const randomBytes = crypto.randomBytes(32).toString('hex');
  return `${prefix}${randomBytes}`;
}

// Generate webhook secret
function generateWebhookSecret(): string {
  return crypto.randomBytes(32).toString('hex');
} 