import { supabaseAdmin } from '@/lib/supabase';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Use admin client to create user without email verification
    const { data, error } = await supabaseAdmin.client.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Skip email verification
    });

    if (error) {
      console.error('Admin signup error:', error);
      
      // Handle specific error cases
      if (error.message.includes('already registered') || error.code === 'email_exists') {
        return NextResponse.json(
          { error: 'An account with this email already exists' },
          { status: 409 }
        );
      }
      
      return NextResponse.json(
        { error: 'Failed to create account. Please try again.' },
        { status: 500 }
      );
    }

    if (data.user) {
      console.log('User created successfully:', data.user.email);
      
      // Create user record in our database with ONLY existing columns
      try {
        const { error: dbError } = await supabaseAdmin.client
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (dbError) {
          console.error('Error creating user record:', dbError);
          // Don't fail the signup for database errors, user is already created in auth
        } else {
          console.log('User record created in database successfully');
        }
      } catch (dbError) {
        console.error('Database error:', dbError);
        // Don't fail the signup for database errors
      }

      return NextResponse.json({
        success: true,
        message: 'Account created successfully'
      });
    }

    return NextResponse.json(
      { error: 'Failed to create account' },
      { status: 500 }
    );

  } catch (error) {
    console.error('Signup API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 