import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackKYCCompleted, trackError } from '@/lib/analytics';

// Helper functions
function generateMockScore(): number {
  // Generate score between 300-850 (typical credit score range)
  return Math.floor(Math.random() * 551) + 300;
}

function getDecisionFromScore(score: number): string {
  if (score >= 700) return 'approved';
  if (score >= 600) return 'conditional';
  return 'declined';
}

function getRiskLevelFromScore(score: number): string {
  if (score >= 750) return 'low';
  if (score >= 650) return 'medium';
  if (score >= 550) return 'high';
  return 'very_high';
}

function getCreditLimitFromScore(score: number): number {
  if (score >= 750) return 50000000; // 500,000 PYG
  if (score >= 700) return 30000000; // 300,000 PYG
  if (score >= 650) return 20000000; // 200,000 PYG
  if (score >= 600) return 10000000; // 100,000 PYG
  return 5000000; // 50,000 PYG
}

function getReasons(decision: string): string[] {
  const reasons = {
    approved: ['Strong credit history', 'Stable income verified', 'Low debt-to-income ratio'],
    conditional: ['Average credit score', 'Additional verification required', 'Limited credit history'],
    declined: ['Insufficient credit history', 'High debt-to-income ratio', 'Recent payment issues'],
  };
  return reasons[decision as keyof typeof reasons] || [];
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, documentNumber, fullName, email } = body;

    // Validate required fields
    if (!userId || !documentNumber || !fullName) {
      return NextResponse.json(
        { error: 'userId, documentNumber, and fullName are required' },
        { status: 400 },
      );
    }

    // Simulate API delay
    await new Promise((resolve) => {
      setTimeout(resolve, 2000);
    });

    const supabase = supabaseAdmin.client;
    const score = generateMockScore();
    const decision = getDecisionFromScore(score);
    const riskLevel = getRiskLevelFromScore(score);
    const creditLimit = getCreditLimitFromScore(score);

    // Create KYC check record
    const kycData = {
      user_id: userId,
      status: decision === 'approved' ? 'approved' : decision === 'conditional' ? 'pending' : 'rejected',
      provider: 'equifax_mock',
      provider_response: {
        score,
        decision,
        risk_level: riskLevel,
        credit_limit: creditLimit,
        reasons: getReasons(decision),
        document_verified: true,
        identity_verified: decision !== 'declined',
        address_verified: Math.random() > 0.3,
      },
      document_number: documentNumber,
      full_name: fullName,
      email,
    };

    const { data: kycCheck, error: kycError } = await supabase
      .from('kyc_checks')
      .insert(kycData)
      .select()
      .single();

    if (kycError) {
      trackError('kyc_creation_failed', {
        error: kycError.message,
        userId,
      });

      return NextResponse.json(
        { error: 'Failed to create KYC check' },
        { status: 500 },
      );
    }

    // Update user record with KYC status
    await supabase
      .from('users')
      .update({
        kyc_status: kycCheck.status,
        credit_limit: decision === 'approved' ? creditLimit : 0,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    // Log event
    await supabase.from('events').insert({
      type: 'kyc_completed',
      user_id: userId,
      metadata: {
        kyc_id: kycCheck.id,
        status: kycCheck.status,
        score,
        decision,
      },
    });

    // Track analytics
    trackKYCCompleted({
      userId,
      status: kycCheck.status,
      score,
      decision,
    });

    return NextResponse.json({
      success: true,
      data: {
        kycId: kycCheck.id,
        status: kycCheck.status,
        score,
        decision,
        riskLevel,
        creditLimit: decision === 'approved' ? creditLimit : 0,
        reasons: getReasons(decision),
        documentVerified: true,
        identityVerified: decision !== 'declined',
        addressVerified: kycData.provider_response.address_verified,
      },
    });
  } catch (error) {
    trackError('kyc_api_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
} 