import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { generateAlphaQR } from '@/lib/qr-generator';
import { trackCheckoutCreated, trackError } from '@/lib/analytics';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { amount, userId, description } = body;

    // Validate required fields
    if (!amount || !userId) {
      return NextResponse.json(
        { error: 'Amount and userId are required' },
        { status: 400 },
      );
    }

    // Validate amount
    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 },
      );
    }

    // Generate unique checkout ID
    const checkoutId = `checkout_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const supabase = supabaseAdmin.client;

    // Generate SIPAP QR data
    const qrData = await generateAlphaQR({
      amount,
      reference: checkoutId,
      description: description || 'Alpha BNPL Purchase',
    });

    // Create loan record
    const { data: loanData, error: loanError } = await supabase
      .from('loans')
      .insert({
        id: checkoutId,
        user_id: userId,
        amount,
        status: 'pending',
        checkout_data: {
          qr_data: qrData,
          description: description || 'Alpha BNPL Purchase',
          created_at: new Date().toISOString(),
        },
      })
      .select()
      .single();

    if (loanError) {
      trackError('checkout_creation_failed', {
        error: loanError.message,
        userId,
        amount,
      });
      return NextResponse.json(
        { error: 'Failed to create checkout' },
        { status: 500 },
      );
    }

    // Log event
    await supabase.from('events').insert({
      type: 'checkout_created',
      user_id: userId,
      metadata: {
        checkout_id: checkoutId,
        amount,
        description,
      },
    });

    // Track analytics
    trackCheckoutCreated({
      checkoutId,
      amount,
      userId,
      description,
    });

    return NextResponse.json({
      checkoutId,
      qrData,
      amount,
      description: description || 'Alpha BNPL Purchase',
      status: 'pending',
    });
  } catch (error) {
    trackError('checkout_api_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const checkoutId = searchParams.get('checkoutId');

    if (!checkoutId) {
      return NextResponse.json(
        { error: 'checkoutId is required' },
        { status: 400 },
      );
    }

    const supabase = supabaseAdmin.client;

    const { data: loan, error } = await supabase
      .from('loans')
      .select('*')
      .eq('id', checkoutId)
      .single();

    if (error || !loan) {
      return NextResponse.json(
        { error: 'Checkout not found' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      checkoutId: loan.id,
      amount: loan.amount,
      status: loan.status,
      description: loan.checkout_data?.description || 'Alpha BNPL Purchase',
      qrData: loan.checkout_data?.qr_data,
      createdAt: loan.created_at,
      updatedAt: loan.updated_at,
    });
  } catch (error) {
    trackError('checkout_status_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
} 