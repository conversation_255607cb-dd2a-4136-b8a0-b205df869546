import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackError, trackCustomEvent } from '@/lib/analytics';
import crypto from 'crypto';

// Webhook endpoint for merchant notifications
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const signature = request.headers.get('x-alpha-signature');
    
    // For demo purposes, we'll accept any webhook
    // In production, you would verify the signature here
    
    const {
      event,
      loan_id,
      merchant_id,
      customer_email,
      amount,
      status,
      timestamp
    } = body;

    // Log the webhook event
    trackCustomEvent('webhook_received', {
      event,
      loan_id,
      merchant_id,
      customer_email,
      amount,
      status
    });

    // Process different webhook events
    switch (event) {
      case 'loan_decision':
        await handleLoanDecision(body);
        break;
      case 'loan_repayment':
        await handleLoanRepayment(body);
        break;
      case 'loan_completed':
        await handleLoanCompleted(body);
        break;
      case 'loan_defaulted':
        await handleLoanDefaulted(body);
        break;
      default:
        console.log('Unknown webhook event:', event);
    }

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully',
      event,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    trackError('webhook_error', {
      message: (error as Error).message,
      endpoint: '/api/webhooks/merchant'
    });

    return NextResponse.json(
      { 
        success: false, 
        error: 'Webhook processing failed' 
      },
      { status: 500 }
    );
  }
}

async function handleLoanDecision(data: any) {
  const supabase = supabaseAdmin.client;

  try {
    // Update loan status based on decision
    const { error } = await supabase
      .from('loans')
      .update({
        status: data.approved ? 'approved' : 'rejected',
        decision_data: {
          approved: data.approved,
          decision_reason: data.reason,
          processed_at: data.timestamp
        },
        updated_at: new Date().toISOString()
      })
      .eq('id', data.loan_id);

    if (error) {
      throw error;
    }

    // Log analytics event
    trackCustomEvent('loan_decision_webhook', {
      loan_id: data.loan_id,
      approved: data.approved,
      amount: data.amount,
      customer_email: data.customer_email
    });

    console.log(`Loan decision processed: ${data.loan_id} - ${data.approved ? 'Approved' : 'Rejected'}`);
  } catch (error) {
    console.error('Error processing loan decision webhook:', error);
    throw error;
  }
}

async function handleLoanRepayment(data: any) {
  const supabase = supabaseAdmin.client;

  try {
    // Create repayment record
    const { error: repaymentError } = await supabase
      .from('repayments')
      .insert({
        loan_id: data.loan_id,
        user_id: data.user_id,
        amount: data.amount,
        status: 'completed',
        payment_method: data.payment_method || 'webhook',
        reference_number: data.reference_number,
        processed_at: data.timestamp,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (repaymentError) {
      throw repaymentError;
    }

    // Check if loan is fully paid
    const { data: loan, error: loanError } = await supabase
      .from('loans')
      .select('amount, repayments(amount)')
      .eq('id', data.loan_id)
      .single();

    if (loanError) {
      throw loanError;
    }

    const totalRepaid = loan.repayments?.reduce((sum: number, rep: any) => sum + rep.amount, 0) || 0;
    const isFullyPaid = totalRepaid >= loan.amount;

    if (isFullyPaid) {
      // Update loan status to completed
      await supabase
        .from('loans')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', data.loan_id);
    }

    // Log analytics event
    trackCustomEvent('loan_repayment_webhook', {
      loan_id: data.loan_id,
      amount: data.amount,
      payment_method: data.payment_method,
      fully_paid: isFullyPaid
    });

    console.log(`Loan repayment processed: ${data.loan_id} - ${data.amount}`);
  } catch (error) {
    console.error('Error processing loan repayment webhook:', error);
    throw error;
  }
}

async function handleLoanCompleted(data: any) {
  const supabase = supabaseAdmin.client;

  try {
    // Update loan status to completed
    const { error } = await supabase
      .from('loans')
      .update({
        status: 'completed',
        completed_at: data.timestamp,
        updated_at: new Date().toISOString()
      })
      .eq('id', data.loan_id);

    if (error) {
      throw error;
    }

    // Log analytics event
    trackCustomEvent('loan_completed_webhook', {
      loan_id: data.loan_id,
      customer_email: data.customer_email,
      amount: data.amount,
      completion_date: data.timestamp
    });

    console.log(`Loan completed: ${data.loan_id}`);
  } catch (error) {
    console.error('Error processing loan completion webhook:', error);
    throw error;
  }
}

async function handleLoanDefaulted(data: any) {
  const supabase = supabaseAdmin.client;

  try {
    // Update loan status to defaulted
    const { error } = await supabase
      .from('loans')
      .update({
        status: 'defaulted',
        status_reason: data.reason || 'Payment overdue',
        updated_at: new Date().toISOString()
      })
      .eq('id', data.loan_id);

    if (error) {
      throw error;
    }

    // Log analytics event
    trackCustomEvent('loan_defaulted_webhook', {
      loan_id: data.loan_id,
      customer_email: data.customer_email,
      amount: data.amount,
      reason: data.reason
    });

    console.log(`Loan defaulted: ${data.loan_id} - ${data.reason}`);
  } catch (error) {
    console.error('Error processing loan default webhook:', error);
    throw error;
  }
}

// GET method for webhook verification
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const challenge = searchParams.get('challenge');
  
  if (challenge) {
    return NextResponse.json({ challenge });
  }
  
  return NextResponse.json({
    message: 'Alpha BNPL Merchant Webhook Endpoint',
    status: 'active',
    timestamp: new Date().toISOString()
  });
} 