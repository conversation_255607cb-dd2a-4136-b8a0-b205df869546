import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackError, trackCustomEvent } from '@/lib/analytics';

// Get loan repayments
export async function GET(
  request: NextRequest,
  { params }: { params: { loanId: string } }
) {
  try {
    const { loanId } = params;

    if (!loanId) {
      return NextResponse.json(
        { error: 'Loan ID is required' },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Get all repayments for the loan
    const { data: repayments, error: repaymentsError } = await supabase
      .from('repayments')
      .select('*')
      .eq('loan_id', loanId)
      .order('created_at', { ascending: false });

    if (repaymentsError) {
      trackError('repayments_fetch_error', {
        error: repaymentsError.message,
        loanId,
      });

      return NextResponse.json(
        { error: 'Failed to fetch repayments' },
        { status: 500 }
      );
    }

    // Calculate repayment summary
    const totalRepaid = repayments?.reduce((sum, repayment) => sum + repayment.amount, 0) || 0;
    const totalPayments = repayments?.length || 0;

    return NextResponse.json({
      repayments: repayments || [],
      summary: {
        total_repaid: totalRepaid,
        total_payments: totalPayments,
        last_payment: repayments?.[0] || null,
      },
    });

  } catch (error) {
    trackError('repayments_api_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      loanId: params.loanId,
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Process a new repayment
export async function POST(
  request: NextRequest,
  { params }: { params: { loanId: string } }
) {
  try {
    const { loanId } = params;
    const body = await request.json();
    const { amount, payment_method, reference_number, notes } = body;

    if (!loanId || !amount) {
      return NextResponse.json(
        { error: 'Loan ID and amount are required' },
        { status: 400 }
      );
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return NextResponse.json(
        { error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Get loan details
    const { data: loan, error: loanError } = await supabase
      .from('loans')
      .select('*')
      .eq('id', loanId)
      .single();

    if (loanError || !loan) {
      return NextResponse.json(
        { error: 'Loan not found' },
        { status: 404 }
      );
    }

    // Check if loan is active
    if (loan.status !== 'active' && loan.status !== 'approved') {
      return NextResponse.json(
        { error: 'Loan is not active for repayments' },
        { status: 400 }
      );
    }

    // Calculate current balance
    const { data: existingRepayments } = await supabase
      .from('repayments')
      .select('amount')
      .eq('loan_id', loanId);

    const totalRepaid = existingRepayments?.reduce((sum, repayment) => sum + repayment.amount, 0) || 0;
    const remainingBalance = loan.amount - totalRepaid;

    if (amount > remainingBalance) {
      return NextResponse.json(
        { error: `Payment amount exceeds remaining balance of ${remainingBalance} PYG` },
        { status: 400 }
      );
    }

    // Generate repayment ID
    const repaymentId = `repayment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create repayment record
    const repaymentData = {
      id: repaymentId,
      loan_id: loanId,
      user_id: loan.user_id,
      amount,
      payment_method: payment_method || 'bank_transfer',
      reference_number,
      notes,
      status: 'completed',
      processed_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
    };

    const { data: repayment, error: repaymentError } = await supabase
      .from('repayments')
      .insert(repaymentData)
      .select()
      .single();

    if (repaymentError) {
      trackError('repayment_creation_failed', {
        error: repaymentError.message,
        loanId,
        amount,
      });

      return NextResponse.json(
        { error: 'Failed to process repayment' },
        { status: 500 }
      );
    }

    // Check if loan is fully paid
    const newTotalRepaid = totalRepaid + amount;
    const isFullyPaid = newTotalRepaid >= loan.amount;

    // Update loan status if fully paid
    if (isFullyPaid) {
      await supabase
        .from('loans')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', loanId);
    }

    // Log repayment event
    await supabase.from('events').insert({
      type: 'repayment_processed',
      user_id: loan.user_id,
      metadata: {
        loan_id: loanId,
        repayment_id: repaymentId,
        amount,
        remaining_balance: loan.amount - newTotalRepaid,
        is_fully_paid: isFullyPaid,
      },
    });

    // Track analytics
    trackCustomEvent('repayment_processed', {
      loanId,
      repaymentId,
      userId: loan.user_id,
      amount,
      paymentMethod: payment_method,
      isFullyPaid,
      remainingBalance: loan.amount - newTotalRepaid,
    });

    if (isFullyPaid) {
      trackCustomEvent('loan_completed', {
        loanId,
        userId: loan.user_id,
        totalAmount: loan.amount,
        totalRepaid: newTotalRepaid,
      });
    }

    return NextResponse.json({
      success: true,
      repayment: repayment,
      loan_status: {
        total_repaid: newTotalRepaid,
        remaining_balance: loan.amount - newTotalRepaid,
        is_fully_paid: isFullyPaid,
        loan_status: isFullyPaid ? 'completed' : loan.status,
      },
      message: isFullyPaid 
        ? 'Payment processed successfully. Loan is now fully paid!' 
        : 'Payment processed successfully',
    });

  } catch (error) {
    trackError('repayment_processing_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      loanId: params.loanId,
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 