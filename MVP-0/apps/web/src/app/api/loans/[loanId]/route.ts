import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { trackError, trackCustomEvent } from '@/lib/analytics';

// Get loan details
export async function GET(
  request: NextRequest,
  { params }: { params: { loanId: string } }
) {
  try {
    const { loanId } = params;

    if (!loanId) {
      return NextResponse.json(
        { error: 'Loan ID is required' },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Get loan with related data
    const { data: loan, error: loanError } = await supabase
      .from('loans')
      .select(`
        *,
        users!inner(id, email, full_name, kyc_status),
        merchants!inner(id, name, website),
        repayments(*)
      `)
      .eq('id', loanId)
      .single();

    if (loanError || !loan) {
      return NextResponse.json(
        { error: 'Loan not found' },
        { status: 404 }
      );
    }

    // Calculate loan metrics
    const totalRepaid = loan.repayments?.reduce((sum: number, repayment: any) => 
      sum + (repayment.amount || 0), 0) || 0;
    
    const remainingAmount = loan.amount - totalRepaid;
    const isOverdue = new Date() > new Date(loan.due_date) && remainingAmount > 0;
    
    // Calculate next payment due
    let nextPaymentDue = null;
    if (remainingAmount > 0 && loan.installments > 0) {
      const paidInstallments = Math.floor(totalRepaid / loan.installment_amount);
      if (paidInstallments < loan.installments) {
        const nextPaymentDate = new Date(loan.created_at);
        nextPaymentDate.setDate(nextPaymentDate.getDate() + (paidInstallments + 1) * 7); // Weekly payments
        nextPaymentDue = nextPaymentDate.toISOString();
      }
    }

    return NextResponse.json({
      loan: {
        id: loan.id,
        amount: loan.amount,
        status: loan.status,
        interest_rate: loan.interest_rate,
        installments: loan.installments,
        installment_amount: loan.installment_amount,
        due_date: loan.due_date,
        created_at: loan.created_at,
        product_details: loan.product_details,
        merchant_data: loan.merchant_data,
        decision_data: loan.decision_data,
      },
      customer: {
        id: loan.users.id,
        email: loan.users.email,
        full_name: loan.users.full_name,
        kyc_status: loan.users.kyc_status,
      },
      merchant: {
        id: loan.merchants.id,
        name: loan.merchants.name,
        website: loan.merchants.website,
      },
      repayments: loan.repayments || [],
      metrics: {
        total_repaid: totalRepaid,
        remaining_amount: remainingAmount,
        is_overdue: isOverdue,
        next_payment_due: nextPaymentDue,
        payment_progress: loan.amount > 0 ? (totalRepaid / loan.amount) * 100 : 0,
      },
    });

  } catch (error) {
    trackError('loan_fetch_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      loanId: params.loanId,
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Update loan status
export async function PATCH(
  request: NextRequest,
  { params }: { params: { loanId: string } }
) {
  try {
    const { loanId } = params;
    const body = await request.json();
    const { status, reason } = body;

    if (!loanId || !status) {
      return NextResponse.json(
        { error: 'Loan ID and status are required' },
        { status: 400 }
      );
    }

    const validStatuses = ['pending', 'approved', 'active', 'completed', 'defaulted', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    const supabase = supabaseAdmin.client;

    // Update loan status
    const { data: updatedLoan, error: updateError } = await supabase
      .from('loans')
      .update({
        status,
        updated_at: new Date().toISOString(),
        ...(reason && { status_reason: reason }),
      })
      .eq('id', loanId)
      .select()
      .single();

    if (updateError || !updatedLoan) {
      trackError('loan_update_error', {
        error: updateError?.message || 'Update failed',
        loanId,
        status,
      });

      return NextResponse.json(
        { error: 'Failed to update loan status' },
        { status: 500 }
      );
    }

    // Log status change event
    await supabase.from('events').insert({
      type: 'loan_status_changed',
      user_id: updatedLoan.user_id,
      metadata: {
        loan_id: loanId,
        old_status: updatedLoan.status,
        new_status: status,
        reason: reason || null,
      },
    });

    // Track analytics
    trackCustomEvent('loan_status_changed', {
      loanId,
      userId: updatedLoan.user_id,
      oldStatus: updatedLoan.status,
      newStatus: status,
      reason,
    });

    return NextResponse.json({
      success: true,
      loan: updatedLoan,
      message: `Loan status updated to ${status}`,
    });

  } catch (error) {
    trackError('loan_update_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      loanId: params.loanId,
    });

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 