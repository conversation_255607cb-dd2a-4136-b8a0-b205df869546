import type { Metadata, Viewport } from 'next';
import { Sora } from 'next/font/google';
import './globals.css';
import { SupabaseProvider } from '@/providers/SupabaseProvider';
import PostHogProvider from '@/providers/PostHogProvider';

const sora = Sora({
  subsets: ['latin'],
  weight: ['400', '600', '700'],
  variable: '--font-sora',
});

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: 'Alpha BNPL',
  description: 'Buy Now, Pay Later platform for Paraguay',
  keywords: ['BNPL', 'Paraguay', 'Fintech', 'Credit', 'Payments'],
  authors: [{ name: 'Alpha BNPL Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Alpha BNPL',
    description: 'Buy Now, Pay Later platform for Paraguay',
    type: 'website',
    locale: 'es_PY',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Alpha BNPL',
    description: 'Buy Now, Pay Later platform for Paraguay',
  },
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#E02020',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="es" className={sora.variable}>
      <body className="min-h-screen bg-cloud-50 font-sora antialiased">
        <PostHogProvider>
          <SupabaseProvider>
            <main className="relative">
              {children}
            </main>
          </SupabaseProvider>
        </PostHogProvider>
      </body>
    </html>
  );
} 