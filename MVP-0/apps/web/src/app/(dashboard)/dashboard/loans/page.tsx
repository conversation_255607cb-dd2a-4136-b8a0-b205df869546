import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/supabase-server';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import LoanTracker from '@/components/loans/LoanTracker';

export const metadata: Metadata = {
  title: 'Mis Préstamos | Alpha BNPL',
  description: 'Seguimiento completo de tus préstamos y pagos con Alpha BNPL',
};

export default async function LoansPage() {
  const supabase = createServerClient();
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    redirect('/login');
  }

  // Get user profile data
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !userProfile) {
    redirect('/login');
  }

  // Get user loans with repayments and merchant data
  const { data: loans, error: loansError } = await supabase
    .from('loans')
    .select(`
      *,
      merchants!inner(id, name, website),
      repayments(*)
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  const userLoans = loans || [];

  return (
    <div className="min-h-screen bg-cloud-50">
      {/* Header */}
      <DashboardHeader user={userProfile} />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Page Header */}
          <div>
            <h1 className="text-3xl font-bold text-charcoal-900 mb-2">
              Mis Préstamos
            </h1>
            <p className="text-charcoal-600">
              Seguimiento completo de tus préstamos y pagos
            </p>
          </div>

          {/* Loans Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-charcoal-600">Total Préstamos</p>
                  <p className="text-2xl font-bold text-charcoal-900">{userLoans.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-charcoal-600">Activos</p>
                  <p className="text-2xl font-bold text-charcoal-900">
                    {userLoans.filter(loan => loan.status === 'active' || loan.status === 'approved').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-charcoal-600">Monto Total</p>
                  <p className="text-2xl font-bold text-charcoal-900">
                    ₲{userLoans.reduce((sum, loan) => sum + loan.amount, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-charcoal-600">Completados</p>
                  <p className="text-2xl font-bold text-charcoal-900">
                    {userLoans.filter(loan => loan.status === 'completed').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Loan Tracker Component */}
          <LoanTracker loans={userLoans} />
        </div>
      </main>
    </div>
  );
} 