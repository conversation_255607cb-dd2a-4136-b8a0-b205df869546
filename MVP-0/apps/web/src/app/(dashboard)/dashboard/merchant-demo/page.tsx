import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/supabase-server';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import MerchantDemo from '@/components/merchant/MerchantDemo';

export const metadata: Metadata = {
  title: 'Demo de Integración Merchant | Alpha BNPL',
  description: 'Demostración de cómo integrar Alpha BNPL en tu tienda online',
};

export default async function MerchantDemoPage() {
  const supabase = createServerClient();
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    redirect('/login');
  }

  // Get user profile data
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !userProfile) {
    redirect('/login');
  }

  return (
    <div className="min-h-screen bg-cloud-50">
      {/* Header */}
      <DashboardHeader user={userProfile} />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Page Header */}
          <div>
            <h1 className="text-3xl font-bold text-charcoal-900 mb-2">
              Integración Merchant
            </h1>
            <p className="text-charcoal-600">
              Demostración de cómo integrar Alpha BNPL en tu tienda online
            </p>
          </div>

          {/* Merchant Demo Component */}
          <MerchantDemo user={userProfile} />
        </div>
      </main>
    </div>
  );
} 