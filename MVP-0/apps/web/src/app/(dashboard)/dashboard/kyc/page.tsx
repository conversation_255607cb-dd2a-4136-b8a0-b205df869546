import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/supabase-server';
import KYCForm from '@/components/kyc/KYCForm';
import DashboardHeader from '@/components/dashboard/DashboardHeader';

export const metadata: Metadata = {
  title: 'Verificación de Identidad | Alpha BNPL',
  description: 'Completa tu verificación de identidad para acceder a tu límite de crédito',
};

export default async function KYCPage() {
  const supabase = createServerClient();
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    redirect('/login');
  }

  // Get user profile data
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !userProfile) {
    redirect('/login');
  }

  // If already approved, redirect to dashboard
  if (userProfile.kyc_status === 'approved') {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen bg-cloud-50">
      {/* Header */}
      <DashboardHeader user={userProfile} />

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header Section */}
          <div className="text-center">
            <div className="w-16 h-16 bg-alpha-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-alpha-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-charcoal-900 mb-2">
              Verificación de Identidad
            </h1>
            <p className="text-charcoal-600 max-w-2xl mx-auto">
              Para garantizar la seguridad y cumplir con las regulaciones financieras, 
              necesitamos verificar tu identidad. Este proceso es rápido y seguro.
            </p>
          </div>

          {/* Status Banner */}
          {userProfile.kyc_status === 'rejected' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-start gap-4">
                <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-red-900 mb-2">
                    Verificación Rechazada
                  </h3>
                  <p className="text-red-700 mb-4">
                    Tu verificación anterior fue rechazada. Puedes intentar nuevamente 
                    asegurándote de que todos los datos sean correctos y las imágenes sean claras.
                  </p>
                  <div className="text-sm text-red-600">
                    <strong>Motivos comunes de rechazo:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Documento de identidad no válido o vencido</li>
                      <li>Imagen borrosa o ilegible</li>
                      <li>Información inconsistente</li>
                      <li>Documento no coincide con los datos proporcionados</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Benefits Section */}
          <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
            <h2 className="text-xl font-semibold text-charcoal-900 mb-4">
              Beneficios de la Verificación
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="font-medium text-charcoal-900 mb-2">Límite de Crédito</h3>
                <p className="text-sm text-charcoal-600">
                  Accede a tu límite de crédito personalizado para realizar compras
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="font-medium text-charcoal-900 mb-2">Aprobación Instantánea</h3>
                <p className="text-sm text-charcoal-600">
                  Proceso automatizado con respuesta en menos de 2 minutos
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="font-medium text-charcoal-900 mb-2">Máxima Seguridad</h3>
                <p className="text-sm text-charcoal-600">
                  Tus datos están protegidos con encriptación de nivel bancario
                </p>
              </div>
            </div>
          </div>

          {/* KYC Form */}
          <div className="bg-white rounded-xl shadow-sm border border-charcoal-100">
            <div className="p-6 border-b border-charcoal-100">
              <h2 className="text-xl font-semibold text-charcoal-900">
                Información Personal
              </h2>
              <p className="text-charcoal-600 text-sm mt-1">
                Completa todos los campos para verificar tu identidad
              </p>
            </div>
            <KYCForm user={userProfile} />
          </div>

          {/* Security Notice */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-blue-900 text-sm">
                  Tus datos están seguros
                </h4>
                <p className="text-blue-700 text-sm mt-1">
                  Utilizamos encriptación de extremo a extremo y cumplimos con todas las 
                  regulaciones de protección de datos. Tu información nunca será compartida 
                  con terceros sin tu consentimiento.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 