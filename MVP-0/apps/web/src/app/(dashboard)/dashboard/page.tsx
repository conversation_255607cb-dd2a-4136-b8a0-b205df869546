import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { createServerClient } from '@/lib/supabase-server';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import CreditLimitCard from '@/components/dashboard/CreditLimitCard';
import LoanOverview from '@/components/dashboard/LoanOverview';
import QuickActions from '@/components/dashboard/QuickActions';
import RecentActivity from '@/components/dashboard/RecentActivity';

export const metadata: Metadata = {
  title: 'Dashboard | Alpha BNPL',
  description: 'Gestiona tus préstamos y pagos con Alpha BNPL',
};

export default async function DashboardPage() {
  const supabase = createServerClient();
  
  // Check authentication
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    redirect('/login');
  }

  // Get user profile data
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (profileError || !userProfile) {
    redirect('/login');
  }

  // Get user's loans
  const { data: loans, error: loansError } = await supabase
    .from('loans')
    .select(`
      *,
      repayments (*)
    `)
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  // Get recent events
  const { data: recentEvents, error: eventsError } = await supabase
    .from('events')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false })
    .limit(10);

  const dashboardData = {
    user: userProfile,
    loans: loans || [],
    recentEvents: recentEvents || [],
  };

  return (
    <div className="min-h-screen bg-cloud-50">
      {/* Header */}
      <DashboardHeader user={userProfile} />

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div>
            <h1 className="text-2xl font-bold text-charcoal-900 mb-2">
              ¡Hola, {userProfile.full_name || 'Usuario'}!
            </h1>
            <p className="text-charcoal-600">
              Aquí tienes un resumen de tu actividad en Alpha BNPL
            </p>
          </div>

          {/* Top Row - Credit Limit and Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <CreditLimitCard 
                creditLimit={userProfile.credit_limit}
                kycStatus={userProfile.kyc_status}
              />
            </div>
            <div>
              <QuickActions kycStatus={userProfile.kyc_status} />
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Loans Overview */}
            <div className="lg:col-span-2">
              <LoanOverview loans={dashboardData.loans} />
            </div>

            {/* Recent Activity */}
            <div>
              <RecentActivity events={dashboardData.recentEvents} />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 