'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

function AuthCodeErrorContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (errorCode: string | null) => {
    switch (errorCode) {
      case 'no_code':
        return 'No se recibió el código de autenticación. El enlace puede estar incompleto.';
      case 'no_user_data':
        return 'No se pudieron obtener los datos del usuario después de la autenticación.';
      default:
        return error || 'El enlace de autenticación ha expirado o es inválido. Por favor, solicita un nuevo enlace mágico.';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-cloud-50 to-alpha-red-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-charcoal-100 text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="h-8 w-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          
          <h2 className="text-xl font-semibold text-charcoal-900 mb-2">
            Error de Autenticación
          </h2>
          
          <p className="text-charcoal-600 text-sm mb-6">
            {getErrorMessage(error)}
          </p>

          {error && (
            <div className="bg-gray-100 p-3 rounded-lg mb-4">
              <p className="text-xs text-gray-500">
                Código de error: {error}
              </p>
            </div>
          )}
          
          <button
            onClick={() => router.push('/login')}
            className="w-full bg-alpha-red-700 text-white py-2 px-4 rounded-lg hover:bg-alpha-red-800 transition-colors"
          >
            Solicitar nuevo enlace
          </button>
        </div>
      </div>
    </div>
  );
}

export default function AuthCodeErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-cloud-50 to-alpha-red-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando...</p>
        </div>
      </div>
    }>
      <AuthCodeErrorContent />
    </Suspense>
  );
} 