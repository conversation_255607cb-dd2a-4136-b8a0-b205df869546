import { createClient } from '@/lib/supabase';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const next = requestUrl.searchParams.get('next') ?? '/dashboard';
  
  console.log('Auth callback called with:', { 
    code: !!code, 
    origin: requestUrl.origin, 
    next
  });

  if (code) {
    const supabase = createClient();
    
    try {
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('Error exchanging code for session:', error);
        return NextResponse.redirect(`${requestUrl.origin}/login?error=auth_error`);
      }

      if (data.user) {
        console.log('User authenticated:', {
          id: data.user.id,
          email: data.user.email,
        });

        // Create/update user record in database with ONLY existing columns
        try {
          const { error: insertError } = await supabase
            .from('users')
            .upsert({
              id: data.user.id,
              email: data.user.email,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }, {
              onConflict: 'id',
              ignoreDuplicates: false
            });

          if (insertError) {
            console.error('Error creating/updating user:', insertError);
          } else {
            console.log('User record created/updated successfully');
          }
        } catch (dbError) {
          console.error('Database error:', dbError);
        }

        // Redirect to dashboard
        console.log('Redirecting to:', `${requestUrl.origin}${next}`);
        return NextResponse.redirect(`${requestUrl.origin}${next}`);
      }
    } catch (error) {
      console.error('Unexpected error in auth callback:', error);
      return NextResponse.redirect(`${requestUrl.origin}/login?error=unexpected_error`);
    }
  }

  // No code provided - redirect to login
  console.log('No auth code provided, redirecting to login');
  return NextResponse.redirect(`${requestUrl.origin}/login`);
} 