import { Metadata } from 'next';
import LoginForm from '@/components/auth/LoginForm';

export const metadata: Metadata = {
  title: 'Iniciar Sesión | Alpha BNPL',
  description: 'Inicia sesión en Alpha BNPL con tu enlace mágico. Sin contraseñas, completamente seguro.',
};

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-cloud-50 to-alpha-red-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-charcoal-100">
          {/* Alpha Logo */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-alpha-red-700 mb-2">
              Alpha
            </h1>
            <p className="text-charcoal-600 text-sm">
              Compra ahora, paga después
            </p>
          </div>

          {/* Login Form */}
          <LoginForm />

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-charcoal-500">
              Al continuar, aceptas nuestros{' '}
              <a href="/terminos" className="text-alpha-red-700 hover:underline">
                Términos de Servicio
              </a>{' '}
              y{' '}
              <a href="/privacidad" className="text-alpha-red-700 hover:underline">
                Política de Privacidad
              </a>
            </p>
          </div>
        </div>

        {/* Security Badge */}
        <div className="mt-6 text-center">
          <div className="inline-flex items-center gap-2 text-charcoal-500 text-xs">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
            </svg>
            Protegido con encriptación de extremo a extremo
          </div>
        </div>
      </div>
    </div>
  );
} 