@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts - Sora */
@import url('https://fonts.googleapis.com/css2?family=Sora:wght@400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400&display=swap');

/* Alpha Brand System CSS Custom Properties */
:root {
  --color-red-700: #E02020;
  --color-red-500: #FF3B3B;
  --color-red-300: #FF7272;
  --color-charcoal-900: #212121;
  --color-cloud-50: #FAFAFA;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Sora', 'Inter', 'Helvetica Neue', Arial, sans-serif;
  color: var(--color-charcoal-900);
  background-color: var(--color-cloud-50);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--color-red-300);
  outline-offset: 2px;
}

/* Motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Glassmorphism utilities */
.glass-card {
  backdrop-filter: blur(4px);
  background: rgba(250, 250, 250, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-cloud-50);
}

::-webkit-scrollbar-thumb {
  background: var(--color-red-300);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-red-500);
} 