'use client';

import posthog from 'posthog-js';
import { PostHog<PERSON>rovider as P<PERSON>rovider } from 'posthog-js/react';
import { useEffect } from 'react';

if (typeof window !== 'undefined') {
  posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY!, {
    api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
    loaded: (ph) => {
      if (process.env.NODE_ENV === 'development') {
        ph.debug();
      }
    },
    capture_pageview: false,
    capture_pageleave: true,
    mask_all_text: false,
    mask_all_element_attributes: false,
    session_recording: {
      maskTextSelector: '[data-mask], .sensitive, input[type="password"], input[type="email"], input[name*="phone"], input[name*="cedula"], input[name*="document"]',
    },
    property_blacklist: [
      '$device_id',
      '$current_url',
      '$pathname',
      '$referrer',
      '$referring_domain',
    ],
  });
}

interface PostHogProviderProps {
  children: React.ReactNode;
}

export default function PostHogProvider({ children }: PostHogProviderProps) {
  useEffect(() => {
    // Track page views manually
    const handleRouteChange = () => {
      if (typeof window !== 'undefined') {
        posthog.capture('$pageview');
      }
    };

    // Initial page view
    handleRouteChange();

    // Listen for route changes (if using Next.js router)
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <PHProvider client={posthog}>
      {children}
    </PHProvider>
  );
}

// Export PostHog instance for direct usage
export { posthog };

// Utility function to safely capture events
export function captureEvent(event: string, properties?: Record<string, unknown>) {
  if (typeof window !== 'undefined' && posthog) {
    posthog.capture(event, properties);
  }
} 