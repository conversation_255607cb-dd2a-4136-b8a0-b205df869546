'use client';

import React, {
  createContext,
  useState,
  useEffect,
} from 'react';
import { createClient } from '@/lib/supabase';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database';

interface SupabaseContextType {
  supabase: SupabaseClient<Database> | null;
  isLoading: boolean;
  error: string | null;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const [supabase, setSupabase] = useState<SupabaseClient<Database> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // 3 second timeout

    try {
      const client = createClient();
      setSupabase(client);
      setError(null);
      setIsLoading(false);
      clearTimeout(timeoutId);
    } catch (err) {
      console.error('Failed to initialize Supabase client:', err);
      setError(err instanceof Error ? err.message : 'Failed to initialize Supabase client');
      setIsLoading(false);
      clearTimeout(timeoutId);
    }

    return () => clearTimeout(timeoutId);
  }, []);

  const value = React.useMemo(() => ({
    supabase,
    isLoading,
    error,
  }), [supabase, isLoading, error]);

  // Show error state but don't block the UI completely
  if (error) {
    console.warn('Supabase initialization error:', error);
    // Still render children but with null supabase client
    return (
      <SupabaseContext.Provider value={{ supabase: null, isLoading: false, error }}>
        {children}
      </SupabaseContext.Provider>
    );
  }

  // Reduce loading time - show content after 1 second even if still loading
  if (isLoading) {
    return (
      <SupabaseContext.Provider value={value}>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Initializing Alpha BNPL...</p>
          </div>
        </div>
      </SupabaseContext.Provider>
    );
  }

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
}

// Hook to use Supabase context
export function useSupabase() {
  const context = React.useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
} 