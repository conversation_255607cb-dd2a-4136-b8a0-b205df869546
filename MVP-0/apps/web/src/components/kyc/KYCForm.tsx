'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase';
import { Button, Input } from '@/components/ui';
import { trackAuthEvent, trackError } from '@/lib/analytics';
import type { Database } from '@/types/database';

type User = Database['public']['Tables']['users']['Row'];

interface KYCFormProps {
  user: User;
}

interface KYCFormData {
  full_name: string;
  document_type: 'cedula' | 'passport';
  document_number: string;
  birth_date: string;
  phone: string;
  address: string;
  city: string;
  income_range: string;
  employment_status: string;
}

export default function KYCForm({ user }: KYCFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<KYCFormData>({
    full_name: user.full_name || '',
    document_type: 'cedula',
    document_number: '',
    birth_date: '',
    phone: user.phone || '',
    address: '',
    city: '',
    income_range: '',
    employment_status: '',
  });

  const handleInputChange = (field: keyof KYCFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    setError(null);
  };

  const validateForm = (): string | null => {
    if (!formData.full_name.trim()) return 'El nombre completo es requerido';
    if (!formData.document_number.trim()) return 'El número de documento es requerido';
    if (!formData.birth_date) return 'La fecha de nacimiento es requerida';
    if (!formData.phone.trim()) return 'El teléfono es requerido';
    if (!formData.address.trim()) return 'La dirección es requerida';
    if (!formData.city.trim()) return 'La ciudad es requerida';
    if (!formData.income_range) return 'El rango de ingresos es requerido';
    if (!formData.employment_status) return 'El estado laboral es requerido';

    // Validate document number based on type
    if (formData.document_type === 'cedula') {
      const cedulaRegex = /^\d{7,8}$/;
      if (!cedulaRegex.test(formData.document_number)) {
        return 'El número de cédula debe tener 7 u 8 dígitos';
      }
    }

    // Validate phone number (Paraguay format)
    const phoneRegex = /^(\+595|0)9\d{8}$/;
    if (!phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
      return 'El teléfono debe tener formato paraguayo válido (ej: +595981123456)';
    }

    // Validate age (must be 18+)
    const birthDate = new Date(formData.birth_date);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    if (age < 18) {
      return 'Debes ser mayor de 18 años para usar Alpha BNPL';
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Validate form
      const validationError = validateForm();
      if (validationError) {
        throw new Error(validationError);
      }

      const supabase = createClient();

      // Track KYC start
      trackAuthEvent('kyc_started', {
        userId: user.id,
        documentType: formData.document_type,
      });

      // Submit KYC data to mock API for processing
      const kycResponse = await fetch('/api/kyc/mock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          personalInfo: formData,
        }),
      });

      if (!kycResponse.ok) {
        const errorData = await kycResponse.json();
        throw new Error(errorData.error || 'Error al procesar la verificación');
      }

      const kycResult = await kycResponse.json();

      // Update user profile with KYC data and status
      const { error: updateError } = await supabase
        .from('users')
        .update({
          full_name: formData.full_name,
          phone: formData.phone,
          kyc_status: kycResult.status,
          kyc_data: {
            ...formData,
            verification_id: kycResult.verificationId,
            submitted_at: new Date().toISOString(),
          },
          credit_limit: kycResult.status === 'approved' ? kycResult.creditLimit : 0,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) {
        throw updateError;
      }

      // Create KYC check record
      const { error: kycError } = await supabase
        .from('kyc_checks')
        .insert({
          user_id: user.id,
          status: kycResult.status,
          verification_data: {
            ...formData,
            verification_id: kycResult.verificationId,
          },
          equifax_response: kycResult.equifaxData || null,
        });

      if (kycError) {
        console.error('Error creating KYC check record:', kycError);
        // Don't throw here - the main update succeeded
      }

      // Track completion
      trackAuthEvent('kyc_completed', {
        userId: user.id,
        status: kycResult.status,
        creditLimit: kycResult.creditLimit,
      });

      // Redirect based on result
      if (kycResult.status === 'approved') {
        router.push('/dashboard?kyc=approved');
      } else if (kycResult.status === 'rejected') {
        router.push('/dashboard/kyc?status=rejected');
      } else {
        router.push('/dashboard?kyc=pending');
      }

    } catch (error) {
      console.error('KYC submission error:', error);
      
      const errorMessage = error instanceof Error 
        ? error.message 
        : 'Error al procesar la verificación';

      trackError('kyc_submission_failed', {
        error: errorMessage,
        userId: user.id,
      });

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="p-6 space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <svg className="w-5 h-5 text-red-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Full Name */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Nombre Completo *
          </label>
          <Input
            type="text"
            value={formData.full_name}
            onChange={(e) => handleInputChange('full_name', e.target.value)}
            placeholder="Ingresa tu nombre completo"
            required
          />
        </div>

        {/* Document Type */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Tipo de Documento *
          </label>
          <select
            value={formData.document_type}
            onChange={(e) => handleInputChange('document_type', e.target.value as 'cedula' | 'passport')}
            className="w-full px-3 py-2 border border-charcoal-300 rounded-lg focus:ring-2 focus:ring-alpha-red-500 focus:border-alpha-red-500"
            required
          >
            <option value="cedula">Cédula de Identidad</option>
            <option value="passport">Pasaporte</option>
          </select>
        </div>

        {/* Document Number */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Número de Documento *
          </label>
          <Input
            type="text"
            value={formData.document_number}
            onChange={(e) => handleInputChange('document_number', e.target.value)}
            placeholder={formData.document_type === 'cedula' ? 'Ej: 1234567' : 'Ej: AB123456'}
            required
          />
        </div>

        {/* Birth Date */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Fecha de Nacimiento *
          </label>
          <Input
            type="date"
            value={formData.birth_date}
            onChange={(e) => handleInputChange('birth_date', e.target.value)}
            required
          />
        </div>

        {/* Phone */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Teléfono *
          </label>
          <Input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="+595981123456"
            required
          />
        </div>

        {/* Address */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Dirección *
          </label>
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="Ingresa tu dirección completa"
            required
          />
        </div>

        {/* City */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Ciudad *
          </label>
          <select
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            className="w-full px-3 py-2 border border-charcoal-300 rounded-lg focus:ring-2 focus:ring-alpha-red-500 focus:border-alpha-red-500"
            required
          >
            <option value="">Selecciona tu ciudad</option>
            <option value="asuncion">Asunción</option>
            <option value="ciudad_del_este">Ciudad del Este</option>
            <option value="san_lorenzo">San Lorenzo</option>
            <option value="luque">Luque</option>
            <option value="capiata">Capiatá</option>
            <option value="lambare">Lambaré</option>
            <option value="fernando_de_la_mora">Fernando de la Mora</option>
            <option value="limpio">Limpio</option>
            <option value="nemby">Ñemby</option>
            <option value="villa_elisa">Villa Elisa</option>
            <option value="other">Otra</option>
          </select>
        </div>

        {/* Employment Status */}
        <div>
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Estado Laboral *
          </label>
          <select
            value={formData.employment_status}
            onChange={(e) => handleInputChange('employment_status', e.target.value)}
            className="w-full px-3 py-2 border border-charcoal-300 rounded-lg focus:ring-2 focus:ring-alpha-red-500 focus:border-alpha-red-500"
            required
          >
            <option value="">Selecciona tu estado laboral</option>
            <option value="employed">Empleado</option>
            <option value="self_employed">Trabajador Independiente</option>
            <option value="business_owner">Empresario</option>
            <option value="student">Estudiante</option>
            <option value="retired">Jubilado</option>
            <option value="unemployed">Desempleado</option>
          </select>
        </div>

        {/* Income Range */}
        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-charcoal-900 mb-2">
            Rango de Ingresos Mensuales *
          </label>
          <select
            value={formData.income_range}
            onChange={(e) => handleInputChange('income_range', e.target.value)}
            className="w-full px-3 py-2 border border-charcoal-300 rounded-lg focus:ring-2 focus:ring-alpha-red-500 focus:border-alpha-red-500"
            required
          >
            <option value="">Selecciona tu rango de ingresos</option>
            <option value="0-2000000">Menos de ₲2.000.000</option>
            <option value="2000000-5000000">₲2.000.000 - ₲5.000.000</option>
            <option value="5000000-10000000">₲5.000.000 - ₲10.000.000</option>
            <option value="10000000-20000000">₲10.000.000 - ₲20.000.000</option>
            <option value="20000000+">Más de ₲20.000.000</option>
          </select>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="border-t border-charcoal-100 pt-6">
        <div className="bg-charcoal-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-charcoal-900 mb-2">
            Términos y Condiciones
          </h3>
          <div className="text-sm text-charcoal-600 space-y-2">
            <p>
              Al enviar esta información, confirmas que:
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Toda la información proporcionada es verdadera y exacta</li>
              <li>Autorizas a Alpha BNPL a verificar tu información con Equifax</li>
              <li>Aceptas los términos y condiciones de servicio</li>
              <li>Autorizas el procesamiento de tus datos personales</li>
            </ul>
          </div>
        </div>

        <div className="flex gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/dashboard')}
            className="flex-1"
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Procesando...' : 'Verificar Identidad'}
          </Button>
        </div>
      </div>
    </form>
  );
} 