'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled';
}

function Input({
  label,
  error,
  helperText,
  className = '',
  id,
  ...props
}: InputProps) {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className={cn('space-y-1', className)}>
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-charcoal-700"
        >
          {label}
        </label>
      )}
      
      <div className="relative">
        <input
          id={inputId}
          className={cn(
            'block w-full px-3 py-2 border rounded-lg shadow-sm transition-colors duration-200',
            'focus:outline-none focus:ring-2 focus:ring-alpha-red-500 focus:border-transparent',
            error
              ? 'border-red-500 text-red-900 placeholder-red-400 focus:ring-red-500'
              : 'border-charcoal-300 text-charcoal-900 placeholder-charcoal-400 focus:ring-alpha-red-500',
          )}
          {...props}
        />
      </div>

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-charcoal-500">{helperText}</p>
      )}
    </div>
  );
}

Input.defaultProps = {
  label: undefined,
  error: undefined,
  helperText: undefined,
  leftIcon: undefined,
  rightIcon: undefined,
  variant: 'default',
};

export default Input; 