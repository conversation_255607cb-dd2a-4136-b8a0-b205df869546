'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase';
import { trackAuthEvent } from '@/lib/analytics';
import type { Database } from '@/types/database';

type User = Database['public']['Tables']['users']['Row'];

interface DashboardHeaderProps {
  user: User;
}

export default function DashboardHeader({ user }: DashboardHeaderProps) {
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    
    try {
      const supabase = createClient();
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw error;
      }

      trackAuthEvent('logout', { userId: user.id });
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', current: true },
    { name: 'Mis Préstamos', href: '/dashboard/loans', current: false },
    { name: 'KYC', href: '/dashboard/kyc', current: false },
    { name: 'Demo Merchant', href: '/dashboard/merchant-demo', current: false },
  ];

  const getKycStatusBadge = () => {
    const status = user.kyc_status;
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
    };

    const labels = {
      pending: 'Verificación Pendiente',
      approved: 'Verificado',
      rejected: 'Verificación Rechazada',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status]}`}>
        {labels[status]}
      </span>
    );
  };

  return (
    <header className="bg-white shadow-sm border-b border-charcoal-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-alpha-red-700">
              Alpha
            </h1>
            <span className="ml-2 text-sm text-charcoal-500">BNPL</span>
          </div>

          {/* Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a
              href="/dashboard"
              className="text-alpha-red-700 border-b-2 border-alpha-red-700 px-1 pt-1 pb-4 text-sm font-medium"
            >
              Dashboard
            </a>
            <a
              href="/dashboard/loans"
              className="text-charcoal-500 hover:text-charcoal-700 px-1 pt-1 pb-4 text-sm font-medium border-b-2 border-transparent hover:border-charcoal-300"
            >
              Mis Préstamos
            </a>
            <a
              href="/dashboard/kyc"
              className="text-charcoal-500 hover:text-charcoal-700 px-1 pt-1 pb-4 text-sm font-medium border-b-2 border-transparent hover:border-charcoal-300"
            >
              KYC
            </a>
            <a
              href="/dashboard/merchant-demo"
              className="text-charcoal-500 hover:text-charcoal-700 px-1 pt-1 pb-4 text-sm font-medium border-b-2 border-transparent hover:border-charcoal-300"
            >
              Demo Merchant
            </a>
          </nav>

          {/* User Menu */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center gap-3 p-2 rounded-lg hover:bg-cloud-50 transition-colors"
            >
              <div className="text-right">
                <div className="text-sm font-medium text-charcoal-900">
                  {user.full_name || 'Usuario'}
                </div>
                <div className="text-xs text-charcoal-500">
                  {user.email}
                </div>
              </div>
              <div className="w-8 h-8 bg-alpha-red-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-alpha-red-700">
                  {(user.full_name || user.email || 'U').charAt(0).toUpperCase()}
                </span>
              </div>
              <svg className="w-4 h-4 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Dropdown Menu */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-lg border border-charcoal-100 py-2 z-50">
                <div className="px-4 py-3 border-b border-charcoal-100">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-charcoal-900">
                        {user.full_name || 'Usuario'}
                      </div>
                      <div className="text-xs text-charcoal-500">
                        {user.email}
                      </div>
                    </div>
                    {getKycStatusBadge()}
                  </div>
                </div>
                
                <div className="py-1">
                  <a
                    href="/dashboard/profile"
                    className="block px-4 py-2 text-sm text-charcoal-700 hover:bg-cloud-50"
                  >
                    Mi Perfil
                  </a>
                  <a
                    href="/dashboard/settings"
                    className="block px-4 py-2 text-sm text-charcoal-700 hover:bg-cloud-50"
                  >
                    Configuración
                  </a>
                  <a
                    href="/help"
                    className="block px-4 py-2 text-sm text-charcoal-700 hover:bg-cloud-50"
                  >
                    Ayuda
                  </a>
                </div>
                
                <div className="border-t border-charcoal-100 py-1">
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50 disabled:opacity-50"
                  >
                    {isLoggingOut ? 'Cerrando sesión...' : 'Cerrar Sesión'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
} 