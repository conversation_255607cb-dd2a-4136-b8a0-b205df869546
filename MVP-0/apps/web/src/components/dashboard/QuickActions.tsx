'use client';

import { Button } from '@/components/ui';

interface QuickActionsProps {
  kycStatus: 'pending' | 'approved' | 'rejected';
}

export default function QuickActions({ kycStatus }: QuickActionsProps) {
  const actions = [
    {
      id: 'checkout',
      title: 'Nueva Compra',
      description: 'Inicia una nueva compra con QR',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      ),
      href: '/dashboard/checkout',
      enabled: kycStatus === 'approved',
      primary: true,
    },
    {
      id: 'kyc',
      title: 'Verificar Identidad',
      description: 'Completa tu verificación KYC',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      href: '/dashboard/kyc',
      enabled: kycStatus === 'pending',
      primary: kycStatus === 'pending',
    },
    {
      id: 'profile',
      title: 'Mi Perfil',
      description: 'Actualiza tu información',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      href: '/dashboard/profile',
      enabled: true,
      primary: false,
    },
    {
      id: 'support',
      title: 'Ayuda',
      description: 'Contacta soporte',
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      href: '/help',
      enabled: true,
      primary: false,
    },
  ];

  const enabledActions = actions.filter(action => action.enabled);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-charcoal-100">
      {/* Header */}
      <div className="p-6 border-b border-charcoal-100">
        <h3 className="text-lg font-semibold text-charcoal-900">
          Acciones Rápidas
        </h3>
        <p className="text-sm text-charcoal-600 mt-1">
          Accesos directos a las funciones más utilizadas
        </p>
      </div>

      {/* Actions */}
      <div className="p-6 space-y-4">
        {enabledActions.map((action) => (
          <div
            key={action.id}
            className={`group relative overflow-hidden rounded-lg border transition-all duration-200 ${
              action.primary
                ? 'border-alpha-red-200 bg-alpha-red-50 hover:bg-alpha-red-100'
                : 'border-charcoal-100 bg-white hover:bg-cloud-50'
            }`}
          >
            <button
              onClick={() => window.location.href = action.href}
              className="w-full p-4 text-left"
            >
              <div className="flex items-start gap-4">
                <div className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                  action.primary
                    ? 'bg-alpha-red-100 text-alpha-red-700'
                    : 'bg-charcoal-100 text-charcoal-600'
                }`}>
                  {action.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={`font-medium ${
                    action.primary ? 'text-alpha-red-900' : 'text-charcoal-900'
                  }`}>
                    {action.title}
                  </h4>
                  <p className={`text-sm mt-1 ${
                    action.primary ? 'text-alpha-red-700' : 'text-charcoal-600'
                  }`}>
                    {action.description}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <svg 
                    className={`w-5 h-5 transform group-hover:translate-x-1 transition-transform ${
                      action.primary ? 'text-alpha-red-600' : 'text-charcoal-400'
                    }`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </button>
          </div>
        ))}

        {/* Special message for rejected KYC */}
        {kycStatus === 'rejected' && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-red-900 text-sm">
                  Verificación Rechazada
                </h4>
                <p className="text-red-700 text-sm mt-1">
                  Tu verificación de identidad fue rechazada. Contacta soporte para resolver este problema.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-3 border-red-300 text-red-700 hover:bg-red-100"
                  onClick={() => window.location.href = '/help'}
                >
                  Contactar Soporte
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 