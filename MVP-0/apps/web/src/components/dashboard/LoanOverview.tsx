'use client';

import { useState } from 'react';
import { Button } from '@/components/ui';
import type { Database } from '@/types/database';

type Loan = Database['public']['Tables']['loans']['Row'] & {
  repayments?: Database['public']['Tables']['repayments']['Row'][];
};

interface LoanOverviewProps {
  loans: Loan[];
}

export default function LoanOverview({ loans }: LoanOverviewProps) {
  const [activeTab, setActiveTab] = useState<'active' | 'completed'>('active');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-PY', {
      style: 'currency',
      currency: 'PYG',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-PY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800',
      active: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      defaulted: 'bg-red-100 text-red-800',
    };

    const labels = {
      pending: 'Pendiente',
      active: 'Activo',
      completed: 'Completado',
      defaulted: 'En Mora',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status as keyof typeof styles]}`}>
        {labels[status as keyof typeof labels] || status}
      </span>
    );
  };

  const activeLoans = loans.filter(loan => loan.status === 'active' || loan.status === 'pending');
  const completedLoans = loans.filter(loan => loan.status === 'completed' || loan.status === 'defaulted');

  const currentLoans = activeTab === 'active' ? activeLoans : completedLoans;

  const totalActiveAmount = activeLoans.reduce((sum, loan) => sum + loan.amount, 0);
  const totalCompletedAmount = completedLoans.reduce((sum, loan) => sum + loan.amount, 0);

  return (
    <div className="bg-white rounded-xl shadow-sm border border-charcoal-100">
      {/* Header */}
      <div className="p-6 border-b border-charcoal-100">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-charcoal-900">
            Mis Préstamos
          </h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/dashboard/loans'}
          >
            Ver Todos
          </Button>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-lg font-semibold text-blue-700">
              {formatCurrency(totalActiveAmount)}
            </div>
            <div className="text-sm text-blue-600">
              Préstamos Activos
            </div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-lg font-semibold text-green-700">
              {formatCurrency(totalCompletedAmount)}
            </div>
            <div className="text-sm text-green-600">
              Total Pagado
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-charcoal-100">
        <nav className="flex space-x-8 px-6">
          <button
            onClick={() => setActiveTab('active')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'active'
                ? 'border-alpha-red-500 text-alpha-red-600'
                : 'border-transparent text-charcoal-500 hover:text-charcoal-700 hover:border-charcoal-300'
            }`}
          >
            Activos ({activeLoans.length})
          </button>
          <button
            onClick={() => setActiveTab('completed')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'completed'
                ? 'border-alpha-red-500 text-alpha-red-600'
                : 'border-transparent text-charcoal-500 hover:text-charcoal-700 hover:border-charcoal-300'
            }`}
          >
            Historial ({completedLoans.length})
          </button>
        </nav>
      </div>

      {/* Loans List */}
      <div className="p-6">
        {currentLoans.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-charcoal-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-charcoal-900 mb-2">
              {activeTab === 'active' ? 'No tienes préstamos activos' : 'No tienes historial de préstamos'}
            </h4>
            <p className="text-charcoal-600 text-sm mb-4">
              {activeTab === 'active' 
                ? 'Realiza tu primera compra con Alpha BNPL'
                : 'Tus préstamos completados aparecerán aquí'
              }
            </p>
            {activeTab === 'active' && (
              <Button
                variant="primary"
                onClick={() => window.location.href = '/dashboard/checkout'}
              >
                Hacer Primera Compra
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {currentLoans.slice(0, 3).map((loan) => (
              <div
                key={loan.id}
                className="flex items-center justify-between p-4 border border-charcoal-100 rounded-lg hover:bg-cloud-50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-medium text-charcoal-900">
                      {formatCurrency(loan.amount)}
                    </h4>
                    {getStatusBadge(loan.status)}
                  </div>
                  <div className="text-sm text-charcoal-600">
                    Creado el {formatDate(loan.created_at)}
                  </div>
                  {loan.checkout_data && typeof loan.checkout_data === 'object' && 'description' in loan.checkout_data && (
                    <div className="text-sm text-charcoal-500 mt-1">
                      {loan.checkout_data.description as string}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {loan.status === 'active' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = `/dashboard/loans/${loan.id}`}
                    >
                      Ver Detalles
                    </Button>
                  )}
                  <svg className="w-5 h-5 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            ))}
            
            {currentLoans.length > 3 && (
              <div className="text-center pt-4">
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/dashboard/loans'}
                >
                  Ver {currentLoans.length - 3} préstamos más
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 