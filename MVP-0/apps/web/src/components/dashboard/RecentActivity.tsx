'use client';

import type { Database } from '@/types/database';

type Event = Database['public']['Tables']['events']['Row'];

interface RecentActivityProps {
  events: Event[];
}

export default function RecentActivity({ events }: RecentActivityProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Ahora mismo';
    if (diffInMinutes < 60) return `Hace ${diffInMinutes} min`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `Hace ${diffInHours}h`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `Hace ${diffInDays}d`;
    
    return date.toLocaleDateString('es-PY', {
      month: 'short',
      day: 'numeric',
    });
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'checkout_started':
      case 'checkout_created':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          </div>
        );
      case 'qr_viewed':
      case 'qr_scanned':
        return (
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
            </svg>
          </div>
        );
      case 'kyc_completed':
      case 'kyc_started':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
      case 'payment_received':
      case 'loan_activated':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
        );
      case 'user_signup':
      case 'user_login':
        return (
          <div className="w-8 h-8 bg-alpha-red-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-alpha-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        );
      default:
        return (
          <div className="w-8 h-8 bg-charcoal-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-charcoal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        );
    }
  };

  const getEventDescription = (event: Event) => {
    const metadata = event.metadata as Record<string, any> || {};
    
    switch (event.type) {
      case 'checkout_started':
        return `Iniciaste una compra por ${metadata.amount ? new Intl.NumberFormat('es-PY', { style: 'currency', currency: 'PYG', minimumFractionDigits: 0 }).format(metadata.amount) : 'un monto'}`;
      case 'checkout_created':
        return `Se creó un checkout para tu compra`;
      case 'qr_viewed':
        return `Visualizaste el código QR de pago`;
      case 'qr_scanned':
        return `Escaneaste el código QR de pago`;
      case 'kyc_completed':
        return `Completaste la verificación de identidad`;
      case 'kyc_started':
        return `Iniciaste la verificación de identidad`;
      case 'payment_received':
        return `Se recibió tu pago`;
      case 'loan_activated':
        return `Tu préstamo fue activado`;
      case 'user_signup':
        return `Te registraste en Alpha BNPL`;
      case 'user_login':
        return `Iniciaste sesión`;
      default:
        return `Actividad: ${event.type}`;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-charcoal-100">
      {/* Header */}
      <div className="p-6 border-b border-charcoal-100">
        <h3 className="text-lg font-semibold text-charcoal-900">
          Actividad Reciente
        </h3>
        <p className="text-sm text-charcoal-600 mt-1">
          Tus últimas acciones en Alpha BNPL
        </p>
      </div>

      {/* Events List */}
      <div className="p-6">
        {events.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-charcoal-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-charcoal-900 mb-2">
              No hay actividad reciente
            </h4>
            <p className="text-charcoal-600 text-sm">
              Tu actividad aparecerá aquí cuando uses Alpha BNPL
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {events.slice(0, 5).map((event) => (
              <div key={event.id} className="flex items-start gap-4">
                {getEventIcon(event.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-charcoal-900">
                    {getEventDescription(event)}
                  </p>
                  <p className="text-xs text-charcoal-500 mt-1">
                    {formatDate(event.created_at)}
                  </p>
                </div>
              </div>
            ))}
            
            {events.length > 5 && (
              <div className="pt-4 border-t border-charcoal-100">
                <button
                  onClick={() => window.location.href = '/dashboard/activity'}
                  className="text-sm text-alpha-red-600 hover:text-alpha-red-700 font-medium"
                >
                  Ver toda la actividad →
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
} 