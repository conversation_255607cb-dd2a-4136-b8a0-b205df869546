'use client';

import { useState } from 'react';
import { Button } from '@/components/ui';

interface CreditLimitCardProps {
  creditLimit: number;
  kycStatus: 'pending' | 'approved' | 'rejected';
}

export default function CreditLimitCard({ creditLimit, kycStatus }: CreditLimitCardProps) {
  const [showDetails, setShowDetails] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-PY', {
      style: 'currency',
      currency: 'PYG',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getKycMessage = () => {
    switch (kycStatus) {
      case 'pending':
        return {
          title: 'Verificación Pendiente',
          message: 'Completa tu verificación de identidad para acceder a tu límite de crédito',
          action: 'Completar Verificación',
          actionHref: '/dashboard/kyc',
          color: 'yellow',
        };
      case 'rejected':
        return {
          title: 'Verificación Rechazada',
          message: 'Tu verificación fue rechazada. Contacta soporte para más información',
          action: 'Contactar Soporte',
          actionHref: '/help',
          color: 'red',
        };
      case 'approved':
        return {
          title: 'Cuenta Verificada',
          message: 'Tu cuenta está completamente verificada y lista para usar',
          action: 'Hacer Compra',
          actionHref: '/dashboard/checkout',
          color: 'green',
        };
    }
  };

  const kycInfo = getKycMessage();
  const availableCredit = kycStatus === 'approved' ? creditLimit : 0;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-alpha-red-700 to-alpha-red-500 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-white">
              Límite de Crédito
            </h3>
            <p className="text-alpha-red-100 text-sm">
              Disponible para compras
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-white">
              {formatCurrency(availableCredit)}
            </div>
            {kycStatus === 'approved' && (
              <div className="text-alpha-red-100 text-sm">
                de {formatCurrency(creditLimit)}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* KYC Status */}
        <div className={`p-4 rounded-lg border-l-4 mb-4 ${
          kycInfo.color === 'yellow' 
            ? 'bg-yellow-50 border-yellow-400' 
            : kycInfo.color === 'red'
            ? 'bg-red-50 border-red-400'
            : 'bg-green-50 border-green-400'
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className={`font-medium ${
                kycInfo.color === 'yellow'
                  ? 'text-yellow-800'
                  : kycInfo.color === 'red'
                  ? 'text-red-800'
                  : 'text-green-800'
              }`}>
                {kycInfo.title}
              </h4>
              <p className={`text-sm mt-1 ${
                kycInfo.color === 'yellow'
                  ? 'text-yellow-700'
                  : kycInfo.color === 'red'
                  ? 'text-red-700'
                  : 'text-green-700'
              }`}>
                {kycInfo.message}
              </p>
            </div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              kycInfo.color === 'yellow'
                ? 'bg-yellow-100'
                : kycInfo.color === 'red'
                ? 'bg-red-100'
                : 'bg-green-100'
            }`}>
              {kycStatus === 'pending' && (
                <svg className="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              )}
              {kycStatus === 'rejected' && (
                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
              {kycStatus === 'approved' && (
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex gap-3">
          <Button
            variant={kycStatus === 'approved' ? 'primary' : 'outline'}
            className="flex-1"
            onClick={() => window.location.href = kycInfo.actionHref}
          >
            {kycInfo.action}
          </Button>
          
          {kycStatus === 'approved' && (
            <Button
              variant="outline"
              onClick={() => setShowDetails(!showDetails)}
              className="px-3"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </Button>
          )}
        </div>

        {/* Details Expansion */}
        {showDetails && kycStatus === 'approved' && (
          <div className="mt-4 pt-4 border-t border-charcoal-100">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-charcoal-600">Límite Total:</span>
                <span className="font-medium">{formatCurrency(creditLimit)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-charcoal-600">Disponible:</span>
                <span className="font-medium text-green-600">{formatCurrency(availableCredit)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-charcoal-600">En Uso:</span>
                <span className="font-medium">{formatCurrency(creditLimit - availableCredit)}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 