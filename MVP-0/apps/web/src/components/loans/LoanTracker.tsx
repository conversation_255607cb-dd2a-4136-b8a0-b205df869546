'use client';

import { useState } from 'react';
import type { Database } from '@/types/database';

type Loan = Database['public']['Tables']['loans']['Row'] & {
  merchants?: Database['public']['Tables']['merchants']['Row'];
  repayments?: Database['public']['Tables']['repayments']['Row'][];
};

interface LoanTrackerProps {
  loans: Loan[];
}

export default function LoanTracker({ loans }: LoanTrackerProps) {
  const [selectedTab, setSelectedTab] = useState<'all' | 'active' | 'completed'>('all');
  const [selectedLoan, setSelectedLoan] = useState<string | null>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-PY', {
      style: 'currency',
      currency: 'PYG',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-PY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: 'Pendiente', color: 'bg-yellow-100 text-yellow-800' },
      approved: { label: 'Aprobado', color: 'bg-blue-100 text-blue-800' },
      active: { label: 'Activo', color: 'bg-green-100 text-green-800' },
      completed: { label: 'Completado', color: 'bg-gray-100 text-gray-800' },
      rejected: { label: 'Rechazado', color: 'bg-red-100 text-red-800' },
      defaulted: { label: 'En Mora', color: 'bg-red-100 text-red-800' },
      cancelled: { label: 'Cancelado', color: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const calculateLoanMetrics = (loan: Loan) => {
    const totalRepaid = loan.repayments?.reduce((sum, repayment) => sum + repayment.amount, 0) || 0;
    const remainingAmount = loan.amount - totalRepaid;
    const paymentProgress = loan.amount > 0 ? (totalRepaid / loan.amount) * 100 : 0;
    const isOverdue = new Date() > new Date(loan.due_date) && remainingAmount > 0;

    return {
      totalRepaid,
      remainingAmount,
      paymentProgress,
      isOverdue,
    };
  };

  const filterLoans = (loans: Loan[]) => {
    switch (selectedTab) {
      case 'active':
        return loans.filter(loan => loan.status === 'active' || loan.status === 'approved');
      case 'completed':
        return loans.filter(loan => loan.status === 'completed');
      default:
        return loans;
    }
  };

  const filteredLoans = filterLoans(loans);

  if (loans.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-12 text-center">
        <div className="w-16 h-16 bg-charcoal-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-charcoal-900 mb-2">
          No tienes préstamos aún
        </h3>
        <p className="text-charcoal-600 mb-6">
          Cuando realices tu primera compra con Alpha BNPL, aparecerá aquí
        </p>
        <button className="bg-alpha-red-600 hover:bg-alpha-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
          Explorar Tiendas
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-1">
        <div className="flex space-x-1">
          {[
            { key: 'all', label: 'Todos', count: loans.length },
            { key: 'active', label: 'Activos', count: loans.filter(l => l.status === 'active' || l.status === 'approved').length },
            { key: 'completed', label: 'Completados', count: loans.filter(l => l.status === 'completed').length },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setSelectedTab(tab.key as typeof selectedTab)}
              className={`flex-1 py-2 px-4 text-sm font-medium rounded-lg transition-colors ${
                selectedTab === tab.key
                  ? 'bg-alpha-red-100 text-alpha-red-700'
                  : 'text-charcoal-600 hover:text-charcoal-900 hover:bg-charcoal-50'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {/* Loans List */}
      <div className="space-y-4">
        {filteredLoans.map((loan) => {
          const metrics = calculateLoanMetrics(loan);
          const isExpanded = selectedLoan === loan.id;

          return (
            <div key={loan.id} className="bg-white rounded-xl shadow-sm border border-charcoal-100 overflow-hidden">
              {/* Loan Header */}
              <div 
                className="p-6 cursor-pointer hover:bg-charcoal-25 transition-colors"
                onClick={() => setSelectedLoan(isExpanded ? null : loan.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-charcoal-900">
                        {loan.merchants?.name || 'Tienda'}
                      </h3>
                      {getStatusBadge(loan.status)}
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-charcoal-500">Monto</p>
                        <p className="font-medium text-charcoal-900">{formatCurrency(loan.amount)}</p>
                      </div>
                      <div>
                        <p className="text-charcoal-500">Pagado</p>
                        <p className="font-medium text-charcoal-900">{formatCurrency(metrics.totalRepaid)}</p>
                      </div>
                      <div>
                        <p className="text-charcoal-500">Restante</p>
                        <p className="font-medium text-charcoal-900">{formatCurrency(metrics.remainingAmount)}</p>
                      </div>
                      <div>
                        <p className="text-charcoal-500">Fecha</p>
                        <p className="font-medium text-charcoal-900">{formatDate(loan.created_at)}</p>
                      </div>
                    </div>
                  </div>
                  <div className="ml-4">
                    <svg 
                      className={`w-5 h-5 text-charcoal-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>

                {/* Progress Bar */}
                {metrics.remainingAmount > 0 && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span className="text-charcoal-600">Progreso de pago</span>
                      <span className="text-charcoal-900 font-medium">{Math.round(metrics.paymentProgress)}%</span>
                    </div>
                    <div className="w-full bg-charcoal-100 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all ${
                          metrics.isOverdue ? 'bg-red-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${Math.min(metrics.paymentProgress, 100)}%` }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Expanded Details */}
              {isExpanded && (
                <div className="border-t border-charcoal-100 p-6 space-y-6">
                  {/* Loan Details */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-charcoal-900 mb-3">Detalles del Préstamo</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-charcoal-600">ID del Préstamo:</span>
                          <span className="font-mono text-charcoal-900">{loan.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-charcoal-600">Tasa de Interés:</span>
                          <span className="text-charcoal-900">{((loan.interest_rate || 0) * 100).toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-charcoal-600">Cuotas:</span>
                          <span className="text-charcoal-900">{loan.installments || 1}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-charcoal-600">Monto por Cuota:</span>
                          <span className="text-charcoal-900">{formatCurrency(loan.installment_amount || loan.amount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-charcoal-600">Fecha de Vencimiento:</span>
                          <span className="text-charcoal-900">{formatDate(loan.due_date)}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-charcoal-900 mb-3">Información del Producto</h4>
                      <div className="text-sm text-charcoal-600">
                        {loan.product_details ? (
                          <div className="bg-charcoal-50 rounded-lg p-3">
                            <pre className="whitespace-pre-wrap text-xs">
                              {JSON.stringify(loan.product_details, null, 2)}
                            </pre>
                          </div>
                        ) : (
                          <p>No hay detalles del producto disponibles</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Payment History */}
                  {loan.repayments && loan.repayments.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-charcoal-900 mb-3">Historial de Pagos</h4>
                      <div className="space-y-2">
                        {loan.repayments
                          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                          .map((repayment) => (
                          <div 
                            key={repayment.id} 
                            className="flex items-center justify-between p-3 bg-charcoal-50 rounded-lg"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                </svg>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-charcoal-900">
                                  {formatCurrency(repayment.amount)}
                                </p>
                                <p className="text-xs text-charcoal-600">
                                  {formatDate(repayment.created_at)}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-xs text-charcoal-600">
                                {repayment.payment_method || 'Transferencia'}
                              </p>
                              {repayment.reference_number && (
                                <p className="text-xs font-mono text-charcoal-500">
                                  Ref: {repayment.reference_number}
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  {(loan.status === 'active' || loan.status === 'approved') && metrics.remainingAmount > 0 && (
                    <div className="flex gap-3 pt-4 border-t border-charcoal-100">
                      <button className="flex-1 bg-alpha-red-600 hover:bg-alpha-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        Realizar Pago
                      </button>
                      <button className="px-4 py-2 text-charcoal-600 hover:text-charcoal-900 font-medium transition-colors">
                        Ver Detalles
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {filteredLoans.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-8 text-center">
          <div className="w-12 h-12 bg-charcoal-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-6 h-6 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-charcoal-900 mb-2">
            No hay préstamos en esta categoría
          </h3>
          <p className="text-charcoal-600">
            {selectedTab === 'active' && 'No tienes préstamos activos en este momento'}
            {selectedTab === 'completed' && 'No has completado ningún préstamo aún'}
          </p>
        </div>
      )}
    </div>
  );
} 