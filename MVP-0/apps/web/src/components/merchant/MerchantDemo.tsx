'use client';

import { useState } from 'react';
import type { Database } from '@/types/database';

type User = Database['public']['Tables']['users']['Row'];

interface MerchantDemoProps {
  user: User;
}

export default function MerchantDemo({ user }: MerchantDemoProps) {
  const [demoProduct, setDemoProduct] = useState({
    name: 'Smartphone Samsung Galaxy A54',
    price: 2500000, // 2,500,000 PYG
    description: 'Smartphone con cámara de 50MP, 128GB de almacenamiento',
    image: '/api/placeholder/400/300',
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleAlphaBNPLRequest = async () => {
    setIsProcessing(true);
    setResult(null);

    try {
      // Simulate merchant API call to Alpha BNPL
      const response = await fetch('/api/merchant/loan-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          merchantId: 'merchant_demo_12345',
          merchantApiKey: 'alpha_demo_key',
          customerEmail: user.email,
          amount: demoProduct.price,
          productDetails: {
            name: demoProduct.name,
            description: demoProduct.description,
            price: demoProduct.price,
          },
          returnUrl: `${window.location.origin}/dashboard`,
          webhookUrl: `${window.location.origin}/api/webhooks/merchant`,
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        error: 'Error de conexión',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-PY', {
      style: 'currency',
      currency: 'PYG',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-8">
      {/* API Documentation */}
      <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
        <h2 className="text-xl font-semibold text-charcoal-900 mb-4">
          Alpha BNPL API Integration
        </h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-2">
              Endpoint de Solicitud de Préstamo
            </h3>
            <div className="bg-charcoal-900 rounded-lg p-4 text-sm">
              <code className="text-green-400">POST</code>
              <code className="text-white ml-2">/api/merchant/loan-request</code>
            </div>
          </div>

          <div>
            <h4 className="text-md font-medium text-charcoal-900 mb-2">
              Parámetros Requeridos
            </h4>
            <div className="bg-charcoal-50 rounded-lg p-4">
              <pre className="text-sm text-charcoal-800 whitespace-pre-wrap">
{`{
  "merchantId": "your_merchant_id",
  "merchantApiKey": "your_api_key",
  "customerEmail": "<EMAIL>",
  "amount": 2500000,
  "productDetails": {
    "name": "Product Name",
    "description": "Product Description",
    "price": 2500000
  },
  "returnUrl": "https://yourstore.com/success",
  "webhookUrl": "https://yourstore.com/webhook"
}`}
              </pre>
            </div>
          </div>

          <div>
            <h4 className="text-md font-medium text-charcoal-900 mb-2">
              Respuesta de Ejemplo
            </h4>
            <div className="bg-charcoal-50 rounded-lg p-4">
              <pre className="text-sm text-charcoal-800 whitespace-pre-wrap">
{`{
  "success": true,
  "approved": true,
  "loan_id": "loan_1234567890_abc123",
  "amount": 2500000,
  "customer_email": "<EMAIL>",
  "installments": 4,
  "installment_amount": 687500,
  "interest_rate": 0.18,
  "due_date": "2024-01-22T10:30:00.000Z",
  "message": "Loan approved successfully"
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>

      {/* Live Demo */}
      <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
        <h2 className="text-xl font-semibold text-charcoal-900 mb-4">
          Demo en Vivo
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Mock E-commerce Product */}
          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-4">
              Producto de Ejemplo
            </h3>
            
            <div className="border border-charcoal-200 rounded-lg p-4 space-y-4">
              <div className="aspect-video bg-charcoal-100 rounded-lg flex items-center justify-center">
                <svg className="w-16 h-16 text-charcoal-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              
              <div>
                <h4 className="font-semibold text-charcoal-900">{demoProduct.name}</h4>
                <p className="text-charcoal-600 text-sm mt-1">{demoProduct.description}</p>
                <p className="text-2xl font-bold text-alpha-red-600 mt-2">
                  {formatCurrency(demoProduct.price)}
                </p>
              </div>

              <div className="space-y-2">
                <button
                  onClick={handleAlphaBNPLRequest}
                  disabled={isProcessing}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                    isProcessing
                      ? 'bg-charcoal-300 text-charcoal-500 cursor-not-allowed'
                      : 'bg-alpha-red-600 hover:bg-alpha-red-700 text-white'
                  }`}
                >
                  {isProcessing ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Procesando...
                    </div>
                  ) : (
                    'Pagar con Alpha BNPL'
                  )}
                </button>
                
                <p className="text-xs text-charcoal-500 text-center">
                  Paga en 4 cuotas sin interés*
                </p>
              </div>
            </div>
          </div>

          {/* Demo Result */}
          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-4">
              Resultado de la API
            </h3>
            
            <div className="border border-charcoal-200 rounded-lg p-4 min-h-[400px]">
              {!result && !isProcessing && (
                <div className="flex items-center justify-center h-full text-charcoal-500">
                  <div className="text-center">
                    <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <p>Haz clic en &quot;Pagar con Alpha BNPL&quot; para ver la respuesta</p>
                  </div>
                </div>
              )}

              {isProcessing && (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-alpha-red-600 mx-auto mb-4"></div>
                    <p className="text-charcoal-600">Procesando solicitud...</p>
                  </div>
                </div>
              )}

              {result && (
                <div className="space-y-4">
                  <div className={`p-3 rounded-lg ${
                    result.success && result.approved
                      ? 'bg-green-50 border border-green-200'
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    <div className="flex items-center">
                      {result.success && result.approved ? (
                        <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      ) : (
                        <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      )}
                      <span className={`font-medium ${
                        result.success && result.approved ? 'text-green-800' : 'text-red-800'
                      }`}>
                        {result.success && result.approved ? 'Préstamo Aprobado' : 'Préstamo Rechazado'}
                      </span>
                    </div>
                  </div>

                  <div className="bg-charcoal-50 rounded-lg p-3">
                    <h4 className="font-medium text-charcoal-900 mb-2">Respuesta JSON:</h4>
                    <pre className="text-xs text-charcoal-700 whitespace-pre-wrap overflow-auto">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>

                  {result.success && result.approved && (
                    <div className="space-y-3">
                      <h4 className="font-medium text-charcoal-900">Detalles del Préstamo:</h4>
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-charcoal-600">ID del Préstamo:</span>
                          <p className="font-mono text-charcoal-900">{result.loan_id}</p>
                        </div>
                        <div>
                          <span className="text-charcoal-600">Monto:</span>
                          <p className="font-medium text-charcoal-900">{formatCurrency(result.amount)}</p>
                        </div>
                        <div>
                          <span className="text-charcoal-600">Cuotas:</span>
                          <p className="font-medium text-charcoal-900">{result.installments}</p>
                        </div>
                        <div>
                          <span className="text-charcoal-600">Monto por Cuota:</span>
                          <p className="font-medium text-charcoal-900">{formatCurrency(result.installment_amount)}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Integration Guide */}
      <div className="bg-white rounded-xl shadow-sm border border-charcoal-100 p-6">
        <h2 className="text-xl font-semibold text-charcoal-900 mb-4">
          Guía de Integración
        </h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-2">
              1. Registro de Merchant
            </h3>
            <p className="text-charcoal-600 mb-3">
              Regístrate como merchant para obtener tus credenciales API:
            </p>
            <div className="bg-charcoal-900 rounded-lg p-4 text-sm">
              <code className="text-green-400">POST</code>
              <code className="text-white ml-2">/api/merchants</code>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-2">
              2. Implementación en tu E-commerce
            </h3>
            <p className="text-charcoal-600 mb-3">
              Agrega el botón de Alpha BNPL en tu página de checkout:
            </p>
            <div className="bg-charcoal-50 rounded-lg p-4">
              <pre className="text-sm text-charcoal-800 whitespace-pre-wrap">
{`// JavaScript Example
async function requestAlphaBNPL() {
  const response = await fetch('/api/merchant/loan-request', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      merchantId: 'your_merchant_id',
      merchantApiKey: 'your_api_key',
      customerEmail: customerEmail,
      amount: totalAmount,
      productDetails: productInfo,
      returnUrl: window.location.origin + '/success',
      webhookUrl: 'https://yoursite.com/webhook'
    })
  });
  
  const result = await response.json();
  if (result.approved) {
    // Redirect to success page
    window.location.href = result.return_url;
  } else {
    // Handle rejection
    alert('Préstamo no aprobado: ' + result.message);
  }
}`}
              </pre>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium text-charcoal-900 mb-2">
              3. Webhooks (Opcional)
            </h3>
            <p className="text-charcoal-600 mb-3">
              Configura webhooks para recibir notificaciones de estado:
            </p>
            <div className="bg-charcoal-50 rounded-lg p-4">
              <pre className="text-sm text-charcoal-800 whitespace-pre-wrap">
{`// Webhook payload example
{
  "event": "loan_decision",
  "loan_id": "loan_1234567890_abc123",
  "approved": true,
  "amount": 2500000,
  "customer_email": "<EMAIL>",
  "timestamp": "2024-01-15T10:30:00.000Z"
}`}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 