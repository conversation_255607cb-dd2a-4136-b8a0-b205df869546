'use client';

import React, { useState, useEffect } from 'react';
import { generateAlphaQR } from '@/lib/qr-generator';
import { trackQRViewed, trackError } from '@/lib/analytics';
import Image from 'next/image';

interface DynamicQRProps {
  checkoutId: string;
  amount: number;
  description?: string;
  onQRGenerated?: (qrData: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

function DynamicQR({
  checkoutId,
  amount,
  description = 'Alpha BNPL Purchase',
  onQRGenerated,
  onError,
  className = '',
}: DynamicQRProps) {
  const [qrDataUrl, setQrDataUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const generateQR = async () => {
      try {
        setIsLoading(true);
        setError('');

        const qrData = await generateAlphaQR({
          amount,
          reference: checkoutId,
          description: description || 'Alpha BNPL Purchase',
        });

        if (qrData) {
          setQrDataUrl(qrData);
          setIsLoading(false);

          // Track QR viewed
          trackQRViewed({
            checkoutId,
            amount,
            timestamp: new Date().toISOString(),
          });

          onQRGenerated?.(qrData);
        } else {
          throw new Error('Failed to generate QR code');
        }
      } catch (error) {
        console.error('Error generating QR:', error);
        setError('Error al generar código QR');
        setIsLoading(false);
        onError?.(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    if (amount && checkoutId) {
      generateQR();
    }
  }, [amount, checkoutId, description, onQRGenerated, onError]);

  if (isLoading) {
    return (
      <div className={`flex flex-col items-center justify-center space-y-4 ${className}`}>
        <div className="animate-spin rounded-full border-4 border-alpha-red-300 border-t-alpha-red-700 size-12" />
        <p className="text-sm text-charcoal-600">Generando código QR...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center space-y-4 ${className}`}>
        <div className="flex items-center space-x-2 text-red-600">
          <svg className="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span className="text-sm font-medium">Error al generar QR</span>
        </div>
        <p className="text-center text-xs text-charcoal-500">{error}</p>
      </div>
    );
  }

  if (!qrDataUrl) {
    return (
      <div className={`flex flex-col items-center justify-center space-y-4 ${className}`}>
        <p className="text-sm text-charcoal-600">No se pudo generar el código QR</p>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center space-y-6 ${className}`}>
      <Image
        src={qrDataUrl}
        alt="Código QR de pago SIPAP"
        width={256}
        height={256}
        className="rounded-lg border-2 border-charcoal-200 bg-white p-4 shadow-lg size-64"
      />

      <div className="text-center">
        <p className="mb-2 text-lg font-semibold text-charcoal-900">
          {amount.toLocaleString('es-PY')}
          {' '}
          PYG
        </p>
        <p className="mb-4 text-sm text-charcoal-600">{description}</p>
        <div className="rounded-lg bg-charcoal-100 p-3">
          <p className="mb-1 text-xs font-medium text-charcoal-700">ID de Compra:</p>
          <p className="font-mono text-xs text-charcoal-600">
            {checkoutId}
          </p>
        </div>
      </div>

      <div className="max-w-sm text-center">
        <h3 className="mb-2 text-sm font-semibold text-charcoal-900">Instrucciones:</h3>
        <ol className="space-y-1 text-xs text-charcoal-600">
          <li className="flex items-start space-x-2">
            <span className="font-semibold">1.</span>
            <span>Abre tu app bancaria con SIPAP habilitado</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="font-semibold">2.</span>
            <span>Escanea este código QR con la cámara de tu teléfono</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="font-semibold">3.</span>
            <span>Confirma el pago en tu aplicación bancaria</span>
          </li>
        </ol>
      </div>
    </div>
  );
}

export default DynamicQR; 