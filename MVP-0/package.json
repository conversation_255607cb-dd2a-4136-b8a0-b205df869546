{"name": "alpha-bnpl", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "packageManager": "npm@10.0.0", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "ci": "turbo run lint test build", "clean": "turbo run clean", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,json}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,md,json}\"", "prepare": "husky install", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "storybook": "turbo run storybook"}, "devDependencies": {"@turbo/gen": "^1.10.12", "husky": "^8.0.3", "lint-staged": "^15.0.2", "prettier": "^3.0.3", "turbo": "^1.10.12"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{md,json}": ["prettier --write"]}, "dependencies": {"clsx": "^2.1.1", "tailwind-merge": "^3.3.1"}}